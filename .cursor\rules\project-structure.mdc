---
description:
globs:
alwaysApply: false
---
# C<PERSON><PERSON> trúc dự án Open-Launch

Open-Launch là một nền tảng ra mắt sản phẩm xây dựng bằng Next.js. Dự án tuân theo cấu trúc App Router của Next.js.

## C<PERSON>u trúc thư mục chính

- `app/`: Chứa tất cả các route và API routes theo cấu trúc App Router của Next.js
- `components/`: Chứa các React components được sử dụng trong toàn bộ ứng dụng
- `drizzle/`: Chứa cấu hình cơ sở dữ liệu và schema sử dụng Drizzle ORM
- `lib/`: Chứa các tiện ích, hooks và logic nghiệp vụ
- `public/`: Chứa tài nguyên tĩnh như hình ảnh và SVG
- `content/`: Chứa nội dung blog và các nội dung tĩnh khác

## Điểm vào chính

- [app/layout.tsx](mdc:app/layout.tsx): Root layout chứa ThemeProvider và các providers khác
- [app/page.tsx](mdc:app/page.tsx): Trang chủ của ứng dụng
- [drizzle/db/schema.ts](mdc:drizzle/db/schema.ts): Schema cơ sở dữ liệu
- [lib/auth.ts](mdc:lib/auth.ts): Cấu hình xác thực
