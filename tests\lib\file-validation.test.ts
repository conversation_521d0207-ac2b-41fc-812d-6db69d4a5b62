/**
 * File Validation Unit Tests
 * 
 * Tests for lib/file-validation.ts
 */

import { describe, it, expect, beforeEach } from 'vitest'
import {
  validateImageFile,
  validateFileName,
  getFileExtension,
  generateFileKey,
  formatFileSize,
  getRecommendedDimensions,
  validateMultipleFiles,
  isImageFile,
  isValidImageExtension,
  ALLOWED_IMAGE_TYPES,
  FILE_SIZE_LIMITS,
} from '@/lib/file-validation'

describe('File Validation', () => {
  let validJpegFile: File
  let validPngFile: File
  let largeFile: File
  let emptyFile: File
  let invalidTypeFile: File

  beforeEach(() => {
    // Create test files
    const jpegBuffer = new Uint8Array([0xFF, 0xD8, 0xFF, 0xE0, ...new Array(1000).fill(0)])
    const pngBuffer = new Uint8Array([0x89, 0x50, 0x4E, 0x47, ...new Array(1000).fill(0)])
    const largeBuffer = new Uint8Array(FILE_SIZE_LIMITS.MAX_FILE_SIZE + 1000)
    const emptyBuffer = new Uint8Array(0)

    validJpegFile = new File([jpegBuffer], 'test.jpg', { type: 'image/jpeg' })
    validPngFile = new File([pngBuffer], 'test.png', { type: 'image/png' })
    largeFile = new File([largeBuffer], 'large.jpg', { type: 'image/jpeg' })
    emptyFile = new File([emptyBuffer], 'empty.jpg', { type: 'image/jpeg' })
    invalidTypeFile = new File([jpegBuffer], 'test.txt', { type: 'text/plain' })
  })

  describe('validateImageFile', () => {
    it('should validate valid JPEG file', () => {
      const result = validateImageFile(validJpegFile, 'logo')
      expect(result).toBeNull()
    })

    it('should validate valid PNG file', () => {
      const result = validateImageFile(validPngFile, 'product-image')
      expect(result).toBeNull()
    })

    it('should reject file that is too large', () => {
      const result = validateImageFile(largeFile, 'logo')
      expect(result).not.toBeNull()
      expect(result?.code).toBe('FILE_TOO_LARGE')
      expect(result?.message).toContain('File size must be less than')
    })

    it('should reject empty file', () => {
      const result = validateImageFile(emptyFile, 'logo')
      expect(result).not.toBeNull()
      expect(result?.code).toBe('FILE_CORRUPTED')
      expect(result?.message).toContain('corrupted')
    })

    it('should reject invalid file type', () => {
      const result = validateImageFile(invalidTypeFile, 'logo')
      expect(result).not.toBeNull()
      expect(result?.code).toBe('INVALID_FILE_TYPE')
      expect(result?.message).toContain('Only image files')
    })

    it('should reject null file', () => {
      const result = validateImageFile(null as any, 'logo')
      expect(result).not.toBeNull()
      expect(result?.code).toBe('NO_FILE')
      expect(result?.message).toBe('No file provided')
    })
  })

  describe('validateFileName', () => {
    it('should validate normal file name', () => {
      const result = validateFileName('test-image.jpg')
      expect(result).toBeNull()
    })

    it('should reject empty file name', () => {
      const result = validateFileName('')
      expect(result).not.toBeNull()
      expect(result?.code).toBe('INVALID_FILE_NAME')
      expect(result?.message).toBe('File name is required')
    })

    it('should reject file name with dangerous characters', () => {
      const dangerousNames = ['test<.jpg', 'test>.jpg', 'test:.jpg', 'test".jpg', 'test/.jpg']
      
      dangerousNames.forEach(name => {
        const result = validateFileName(name)
        expect(result).not.toBeNull()
        expect(result?.code).toBe('INVALID_FILE_NAME')
        expect(result?.message).toContain('invalid characters')
      })
    })

    it('should reject file name that is too long', () => {
      const longName = 'a'.repeat(256) + '.jpg'
      const result = validateFileName(longName)
      expect(result).not.toBeNull()
      expect(result?.code).toBe('INVALID_FILE_NAME')
      expect(result?.message).toContain('too long')
    })
  })

  describe('getFileExtension', () => {
    it('should get extension from file name', () => {
      const jpegFile = new File([], 'test.jpeg', { type: 'image/jpeg' })
      expect(getFileExtension(jpegFile)).toBe('jpeg')
    })

    it('should get extension from MIME type when file name has no extension', () => {
      const jpegFile = new File([], 'test', { type: 'image/jpeg' })
      expect(getFileExtension(jpegFile)).toBe('jpg')
    })

    it('should handle different MIME types', () => {
      const testCases = [
        { type: 'image/jpeg', expected: 'jpg' },
        { type: 'image/png', expected: 'png' },
        { type: 'image/webp', expected: 'webp' },
        { type: 'image/gif', expected: 'gif' },
      ]

      testCases.forEach(({ type, expected }) => {
        const file = new File([], 'test', { type })
        expect(getFileExtension(file)).toBe(expected)
      })
    })

    it('should default to jpg for unknown types', () => {
      const file = new File([], 'test', { type: 'unknown/type' })
      expect(getFileExtension(file)).toBe('jpg')
    })
  })

  describe('generateFileKey', () => {
    it('should generate key for logo', () => {
      const key = generateFileKey('user123', 'logo', validJpegFile)
      expect(key).toMatch(/^projects\/user123\/logo\/\d+-[a-z0-9]+\.jpg$/)
    })

    it('should generate key for product-image', () => {
      const key = generateFileKey('user456', 'product-image', validPngFile)
      expect(key).toMatch(/^projects\/user456\/product-image\/\d+-[a-z0-9]+\.png$/)
    })

    it('should throw error for empty user ID', () => {
      expect(() => generateFileKey('', 'logo')).toThrow('User ID is required')
    })

    it('should work without file parameter', () => {
      const key = generateFileKey('user123', 'logo')
      expect(key).toMatch(/^projects\/user123\/logo\/\d+-[a-z0-9]+\.jpg$/)
    })
  })

  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 Bytes')
      expect(formatFileSize(1024)).toBe('1 KB')
      expect(formatFileSize(1024 * 1024)).toBe('1 MB')
      expect(formatFileSize(1024 * 1024 * 1024)).toBe('1 GB')
    })

    it('should handle decimal places', () => {
      expect(formatFileSize(1536)).toBe('1.5 KB') // 1.5 KB
      expect(formatFileSize(1024 * 1024 * 1.5)).toBe('1.5 MB')
    })
  })

  describe('getRecommendedDimensions', () => {
    it('should return correct dimensions for logo', () => {
      const dims = getRecommendedDimensions('logo')
      expect(dims.width).toBe(256)
      expect(dims.height).toBe(256)
      expect(dims.aspectRatio).toBe('1:1')
    })

    it('should return correct dimensions for product-image', () => {
      const dims = getRecommendedDimensions('product-image')
      expect(dims.width).toBe(800)
      expect(dims.height).toBe(450)
      expect(dims.aspectRatio).toBe('16:9')
    })
  })

  describe('validateMultipleFiles', () => {
    it('should validate multiple files', () => {
      const files = [validJpegFile, validPngFile, largeFile]
      const results = validateMultipleFiles(files, 'logo')
      
      expect(results).toHaveLength(3)
      expect(results[0].error).toBeNull() // valid JPEG
      expect(results[1].error).toBeNull() // valid PNG
      expect(results[2].error).not.toBeNull() // large file
      expect(results[2].error?.code).toBe('FILE_TOO_LARGE')
    })
  })

  describe('isImageFile', () => {
    it('should identify image files correctly', () => {
      expect(isImageFile(validJpegFile)).toBe(true)
      expect(isImageFile(validPngFile)).toBe(true)
      expect(isImageFile(invalidTypeFile)).toBe(false)
    })
  })

  describe('isValidImageExtension', () => {
    it('should validate image extensions', () => {
      expect(isValidImageExtension('jpg')).toBe(true)
      expect(isValidImageExtension('jpeg')).toBe(true)
      expect(isValidImageExtension('png')).toBe(true)
      expect(isValidImageExtension('webp')).toBe(true)
      expect(isValidImageExtension('gif')).toBe(true)
      expect(isValidImageExtension('txt')).toBe(false)
      expect(isValidImageExtension('pdf')).toBe(false)
    })

    it('should be case insensitive', () => {
      expect(isValidImageExtension('JPG')).toBe(true)
      expect(isValidImageExtension('PNG')).toBe(true)
      expect(isValidImageExtension('WEBP')).toBe(true)
    })
  })

  describe('Constants', () => {
    it('should have correct allowed image types', () => {
      expect(ALLOWED_IMAGE_TYPES).toContain('image/jpeg')
      expect(ALLOWED_IMAGE_TYPES).toContain('image/jpg')
      expect(ALLOWED_IMAGE_TYPES).toContain('image/png')
      expect(ALLOWED_IMAGE_TYPES).toContain('image/webp')
      expect(ALLOWED_IMAGE_TYPES).toContain('image/gif')
    })

    it('should have correct file size limits', () => {
      expect(FILE_SIZE_LIMITS.LOGO).toBe(1024 * 1024) // 1MB
      expect(FILE_SIZE_LIMITS.PRODUCT_IMAGE).toBe(1024 * 1024) // 1MB
      expect(FILE_SIZE_LIMITS.MAX_FILE_SIZE).toBe(1024 * 1024) // 1MB
    })
  })
})
