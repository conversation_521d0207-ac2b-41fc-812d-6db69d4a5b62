---
description:
globs:
alwaysApply: false
---
# <PERSON><PERSON> thống xác thực

Open-Launch sử dụng NextAuth.js cho hệ thống xác thực. C<PERSON>u hình xác thực được định nghĩa trong [lib/auth.ts](mdc:lib/auth.ts) và [lib/auth-client.ts](mdc:lib/auth-client.ts).

## C<PERSON>c trang xác thực

- [app/(auth)/sign-in/page.tsx](mdc:app/(auth)/sign-in/page.tsx): Trang đăng nhập
- [app/(auth)/sign-up/page.tsx](mdc:app/(auth)/sign-up/page.tsx): Trang đăng ký
- [app/(auth)/forgot-password/page.tsx](mdc:app/(auth)/forgot-password/page.tsx): Trang quên mật khẩu
- [app/(auth)/reset-password/page.tsx](mdc:app/(auth)/reset-password/page.tsx): Trang đặt lại mật khẩu
- [app/(auth)/verify-email/sent/page.tsx](mdc:app/(auth)/verify-email/sent/page.tsx): Trang xác nhận email đã gửi
- [app/(auth)/verify-email/success/page.tsx](mdc:app/(auth)/verify-email/success/page.tsx): Trang xác nhận email thành công

## Components xác thực

- [components/auth/sign-in-form.tsx](mdc:components/auth/sign-in-form.tsx): Form đăng nhập
- [components/auth/sign-up-form.tsx](mdc:components/auth/sign-up-form.tsx): Form đăng ký
- [components/auth/forgot-password-form.tsx](mdc:components/auth/forgot-password-form.tsx): Form quên mật khẩu
- [components/auth/reset-password-form.tsx](mdc:components/auth/reset-password-form.tsx): Form đặt lại mật khẩu

## API Routes xác thực

- [app/api/auth/[...all]/route.ts](mdc:app/api/auth/[...all]/route.ts): API route xử lý các yêu cầu xác thực

## Validation

- [lib/validations/auth.ts](mdc:lib/validations/auth.ts): Các schema validation cho form xác thực
