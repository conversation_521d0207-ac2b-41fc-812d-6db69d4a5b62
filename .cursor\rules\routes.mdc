---
description:
globs:
alwaysApply: false
---
# <PERSON><PERSON><PERSON> trúc Routes

Open-Launch sử dụng App Router của Next.js để định nghĩa các routes. C<PERSON>u trúc routes được tổ chức trong thư mục [app](mdc:app/).

## Routes chính

- `/`: Trang chủ - [app/page.tsx](mdc:app/page.tsx)
- `/projects/[slug]`: Chi tiết dự án - [app/projects/[slug]/page.tsx](mdc:app/projects/[slug]/page.tsx)
- `/projects/submit`: Gửi dự án mới - [app/projects/submit/page.tsx](mdc:app/projects/submit/page.tsx)
- `/categories`: Danh mục - [app/categories/page.tsx](mdc:app/categories/page.tsx)
- `/trending`: Xu hướng - [app/trending/page.tsx](mdc:app/trending/page.tsx)
- `/winners`: Người chiến thắng - [app/winners/page.tsx](mdc:app/winners/page.tsx)
- `/dashboard`: Bảng điều khiển - [app/dashboard/page.tsx](mdc:app/dashboard/page.tsx)
- `/settings`: Cài đặt - [app/settings/page.tsx](mdc:app/settings/page.tsx)
- `/blog`: Blog - [app/blog/page.tsx](mdc:app/blog/page.tsx)
- `/blog/[slug]`: Chi tiết bài viết - [app/blog/[slug]/page.tsx](mdc:app/blog/[slug]/page.tsx)

## Route Groups

- `(auth)`: Routes liên quan đến xác thực - [app/(auth)](mdc:app/(auth)/)
  - `/sign-in`: Đăng nhập
  - `/sign-up`: Đăng ký
  - `/forgot-password`: Quên mật khẩu
  - `/reset-password`: Đặt lại mật khẩu
  - `/verify-email`: Xác nhận email

## Admin Routes

- `/admin`: Trang quản trị - [app/admin/page.tsx](mdc:app/admin/page.tsx)
- `/admin/projects`: Quản lý dự án - [app/admin/projects/page.tsx](mdc:app/admin/projects/page.tsx)
- `/admin/projects/[projectId]`: Chi tiết dự án (admin) - [app/admin/projects/[projectId]/page.tsx](mdc:app/admin/projects/[projectId]/page.tsx)
- `/admin/projects/[projectId]/edit`: Chỉnh sửa dự án (admin) - [app/admin/projects/[projectId]/edit/page.tsx](mdc:app/admin/projects/[projectId]/edit/page.tsx)

## Layouts

- Root layout: [app/layout.tsx](mdc:app/layout.tsx)
- Auth layout: [app/(auth)/layout.tsx](mdc:app/(auth)/layout.tsx)
- Admin layout: [app/admin/layout.tsx](mdc:app/admin/layout.tsx)
