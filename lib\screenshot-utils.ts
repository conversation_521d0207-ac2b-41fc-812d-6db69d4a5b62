/**
 * Utility functions for ScreenshotOne API integration
 * Docs: https://screenshotone.com/docs/getting-started/
 */

interface ScreenshotOptions {
  url: string
  format?: 'png' | 'jpg' | 'webp'
  viewport_width?: number
  viewport_height?: number
  device_scale_factor?: number
  full_page?: boolean
  block_ads?: boolean
  block_cookie_banners?: boolean
  block_trackers?: boolean
  cache?: boolean
  cache_ttl?: number
}

interface ScreenshotResponse {
  success: boolean
  imageUrl?: string
  error?: string
  details?: string
}

/**
 * Generate screenshot URL using ScreenshotOne API
 * @param options Screenshot configuration options
 * @returns Screenshot URL or null if failed
 */
export function generateScreenshotUrl(options: ScreenshotOptions): string | null {
  const apiKey = process.env.SCREENSHOTONE_ACCESS_KEY
  
  if (!apiKey) {
    console.error('SCREENSHOTONE_ACCESS_KEY is not configured')
    return null
  }

  // Base URL for ScreenshotOne API
  const baseUrl = 'https://api.screenshotone.com/take'
  
  // Default options optimized for product screenshots
  const defaultOptions: Partial<ScreenshotOptions> = {
    format: 'webp',
    viewport_width: 1200,
    viewport_height: 800,
    device_scale_factor: 2,
    full_page: false,
    block_ads: true,
    block_cookie_banners: true,
    block_trackers: true,
    cache: true,
    cache_ttl: 86400, // 24 hours
  }

  // Merge options with defaults
  const finalOptions = { ...defaultOptions, ...options }

  // Build query parameters
  const params = new URLSearchParams({
    access_key: apiKey,
    url: finalOptions.url,
    format: finalOptions.format!,
    viewport_width: finalOptions.viewport_width!.toString(),
    viewport_height: finalOptions.viewport_height!.toString(),
    device_scale_factor: finalOptions.device_scale_factor!.toString(),
    full_page: finalOptions.full_page!.toString(),
    block_ads: finalOptions.block_ads!.toString(),
    block_cookie_banners: finalOptions.block_cookie_banners!.toString(),
    block_trackers: finalOptions.block_trackers!.toString(),
    cache: finalOptions.cache!.toString(),
    cache_ttl: finalOptions.cache_ttl!.toString(),
  })

  return `${baseUrl}?${params.toString()}`
}

/**
 * Take screenshot and upload to S3
 * @param websiteUrl Website URL to screenshot
 * @returns Screenshot response with S3 URL
 */
export async function takeScreenshotAndUpload(websiteUrl: string): Promise<ScreenshotResponse> {
  try {
    // Validate URL
    if (!isValidUrl(websiteUrl)) {
      return {
        success: false,
        error: 'Invalid URL',
        details: 'Please provide a valid website URL'
      }
    }

    // Generate screenshot URL
    const screenshotUrl = generateScreenshotUrl({ url: websiteUrl })
    
    if (!screenshotUrl) {
      return {
        success: false,
        error: 'Configuration Error',
        details: 'ScreenshotOne API is not properly configured'
      }
    }

    // Fetch screenshot
    const response = await fetch(screenshotUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Open-Launch/1.0'
      }
    })

    if (!response.ok) {
      return {
        success: false,
        error: 'Screenshot Failed',
        details: `HTTP ${response.status}: ${response.statusText}`
      }
    }

    // Get image buffer
    const imageBuffer = await response.arrayBuffer()
    
    if (imageBuffer.byteLength === 0) {
      return {
        success: false,
        error: 'Empty Screenshot',
        details: 'Screenshot API returned empty response'
      }
    }

    // Upload to S3
    const uploadResult = await uploadScreenshotToS3(imageBuffer, websiteUrl)
    
    if (!uploadResult.success) {
      return uploadResult
    }

    return {
      success: true,
      imageUrl: uploadResult.imageUrl
    }

  } catch (error) {
    console.error('Screenshot generation failed:', error)
    return {
      success: false,
      error: 'Unexpected Error',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

/**
 * Upload screenshot buffer to S3
 * @param imageBuffer Screenshot image buffer
 * @param websiteUrl Original website URL for naming
 * @returns Upload result
 */
async function uploadScreenshotToS3(imageBuffer: ArrayBuffer, websiteUrl: string): Promise<ScreenshotResponse> {
  try {
    // Import S3 client
    const { uploadToS3 } = await import('@/lib/s3-upload')
    
    // Generate filename from URL
    const domain = new URL(websiteUrl).hostname.replace(/[^a-zA-Z0-9]/g, '-')
    const timestamp = Date.now()
    const filename = `screenshots/${domain}-${timestamp}.webp`
    
    // Convert ArrayBuffer to Buffer
    const buffer = Buffer.from(imageBuffer)
    
    // Create File-like object
    const file = new File([buffer], filename, { type: 'image/webp' })
    
    // Upload to S3
    const uploadResult = await uploadToS3(file, 'product-images')
    
    if (!uploadResult.success || !uploadResult.url) {
      return {
        success: false,
        error: 'Upload Failed',
        details: uploadResult.error || 'Failed to upload screenshot to S3'
      }
    }

    return {
      success: true,
      imageUrl: uploadResult.url
    }

  } catch (error) {
    console.error('S3 upload failed:', error)
    return {
      success: false,
      error: 'Upload Error',
      details: error instanceof Error ? error.message : 'Failed to upload to S3'
    }
  }
}

/**
 * Validate if string is a valid URL
 * @param string URL string to validate
 * @returns True if valid URL
 */
function isValidUrl(string: string): boolean {
  try {
    const url = new URL(string)
    return url.protocol === 'http:' || url.protocol === 'https:'
  } catch {
    return false
  }
}

/**
 * Get screenshot preview URL (for UI preview without uploading)
 * @param websiteUrl Website URL
 * @returns Preview screenshot URL
 */
export function getScreenshotPreviewUrl(websiteUrl: string): string | null {
  if (!isValidUrl(websiteUrl)) {
    return null
  }

  return generateScreenshotUrl({
    url: websiteUrl,
    viewport_width: 600,
    viewport_height: 400,
    device_scale_factor: 1,
  })
}

/**
 * Batch generate screenshots for multiple URLs
 * @param urls Array of website URLs
 * @returns Array of screenshot results
 */
export async function batchTakeScreenshots(urls: string[]): Promise<ScreenshotResponse[]> {
  const results = await Promise.allSettled(
    urls.map(url => takeScreenshotAndUpload(url))
  )

  return results.map((result, index) => {
    if (result.status === 'fulfilled') {
      return result.value
    } else {
      return {
        success: false,
        error: 'Batch Processing Failed',
        details: `Failed to process URL: ${urls[index]}`
      }
    }
  })
}
