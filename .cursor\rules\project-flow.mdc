---
description:
globs:
alwaysApply: false
---
# Luồng hoạt động của dự án

Open-Launch là nền tảng ra mắt sản phẩm, cho phép người dùng gửi dự án và nhận phản hồi từ cộng đồng. Dưới đây là luồng hoạt động chính của dự án.

## Gửi dự án

1. Người dùng đăng ký/đăng nhập vào hệ thống
2. Người dùng truy cập trang gửi dự án (`/projects/submit`)
3. Người dùng điền thông tin dự án (tên, mô tả, URL, hình ảnh, v.v.)
4. <PERSON><PERSON> thống kiểm tra và xác thực thông tin dự án
5. Dự án được gửi để phê duyệt

## Ph<PERSON> duyệt dự án

1. Quản trị viên xem danh sách dự án chờ phê duyệt trong trang quản trị (`/admin/projects`)
2. Quản trị viên xem chi tiết dự án và quyết định phê duyệt hoặc từ chối
3. Nếu phê duyệt, dự án sẽ được lên lịch ra mắt
4. Nếu từ chối, dự án sẽ được gửi lại cho người dùng với lý do từ chối

## Ra mắt dự án

1. Dự án được lên lịch ra mắt vào một ngày cụ thể
2. Vào ngày ra mắt, dự án sẽ xuất hiện trên trang chủ
3. Người dùng có thể upvote và bình luận về dự án
4. Dự án được xếp hạng dựa trên số lượng upvote

## Kết thúc ra mắt

1. Sau khi kết thúc thời gian ra mắt (thường là 24 giờ), dự án sẽ không còn xuất hiện trên trang chủ
2. Dự án với số lượng upvote cao nhất sẽ được công bố là người chiến thắng
3. Người chiến thắng sẽ được hiển thị trong trang Winners

## Các trạng thái dự án

- `PENDING`: Dự án đang chờ phê duyệt
- `APPROVED`: Dự án đã được phê duyệt và đang chờ ra mắt
- `REJECTED`: Dự án bị từ chối
- `LIVE`: Dự án đang trong thời gian ra mắt
- `COMPLETED`: Dự án đã kết thúc ra mắt

## Các tác vụ định kỳ

- Cập nhật trạng thái ra mắt: [app/api/cron/update-launches/route.ts](mdc:app/api/cron/update-launches/route.ts)
- Gửi thông báo cho người chiến thắng: [app/api/cron/send-winner-notifications/route.ts](mdc:app/api/cron/send-winner-notifications/route.ts)
- Gửi nhắc nhở cho các ra mắt đang diễn ra: [app/api/cron/send-ongoing-reminders/route.ts](mdc:app/api/cron/send-ongoing-reminders/route.ts)
