{"id": "cb411e2c-0458-44bd-b4b6-80edb923eda6", "prevId": "4c0d17e5-be93-4472-84d6-2beea8e61a14", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.category": {"name": "category", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"category_name_idx": {"name": "category_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"category_name_unique": {"name": "category_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fuma_comments": {"name": "fuma_comments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "page": {"name": "page", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "thread": {"name": "thread", "type": "integer", "primaryKey": false, "notNull": false}, "author": {"name": "author", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "json", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fuma_rates": {"name": "fuma_rates", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "comment_id": {"name": "comment_id", "type": "integer", "primaryKey": false, "notNull": true}, "like": {"name": "like", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {"comment_idx": {"name": "comment_idx", "columns": [{"expression": "comment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {"fuma_rates_user_id_comment_id_pk": {"name": "fuma_rates_user_id_comment_id_pk", "columns": ["user_id", "comment_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fuma_roles": {"name": "fuma_roles", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "can_delete": {"name": "can_delete", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.launch_quota": {"name": "launch_quota", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "free_count": {"name": "free_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "premium_count": {"name": "premium_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "premium_plus_count": {"name": "premium_plus_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"launch_quota_date_unique": {"name": "launch_quota_date_unique", "nullsNotDistinct": false, "columns": ["date"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project": {"name": "project", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "website_url": {"name": "website_url", "type": "text", "primaryKey": false, "notNull": true}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": true}, "cover_image_url": {"name": "cover_image_url", "type": "text", "primaryKey": false, "notNull": false}, "product_image": {"name": "product_image", "type": "text", "primaryKey": false, "notNull": false}, "github_url": {"name": "github_url", "type": "text", "primaryKey": false, "notNull": false}, "twitter_url": {"name": "twitter_url", "type": "text", "primaryKey": false, "notNull": false}, "tech_stack": {"name": "tech_stack", "type": "text[]", "primaryKey": false, "notNull": false}, "pricing": {"name": "pricing", "type": "text", "primaryKey": false, "notNull": true, "default": "'free'"}, "platforms": {"name": "platforms", "type": "text[]", "primaryKey": false, "notNull": false}, "launch_status": {"name": "launch_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "scheduled_launch_date": {"name": "scheduled_launch_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "launch_type": {"name": "launch_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'free'"}, "featured_on_homepage": {"name": "featured_on_homepage", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "daily_ranking": {"name": "daily_ranking", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"project_name_idx": {"name": "project_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"project_created_by_user_id_fk": {"name": "project_created_by_user_id_fk", "tableFrom": "project", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"project_slug_unique": {"name": "project_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_to_category": {"name": "project_to_category", "schema": "", "columns": {"project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"project_to_category_project_id_project_id_fk": {"name": "project_to_category_project_id_project_id_fk", "tableFrom": "project_to_category", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_to_category_category_id_category_id_fk": {"name": "project_to_category_category_id_category_id_fk", "tableFrom": "project_to_category", "tableTo": "category", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"project_to_category_project_id_category_id_pk": {"name": "project_to_category_project_id_category_id_pk", "columns": ["project_id", "category_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.seo_article": {"name": "seo_article", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "meta_title": {"name": "meta_title", "type": "text", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "text", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"seo_article_slug_idx": {"name": "seo_article_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"seo_article_slug_unique": {"name": "seo_article_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "impersonated_by": {"name": "impersonated_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.upvote": {"name": "upvote", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"upvote_user_id_user_id_fk": {"name": "upvote_user_id_user_id_fk", "tableFrom": "upvote", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "upvote_project_id_project_id_fk": {"name": "upvote_project_id_project_id_fk", "tableFrom": "upvote", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "banned": {"name": "banned", "type": "boolean", "primaryKey": false, "notNull": false}, "ban_reason": {"name": "ban_reason", "type": "text", "primaryKey": false, "notNull": false}, "ban_expires": {"name": "ban_expires", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.waitlist_submission": {"name": "waitlist_submission", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "project_url": {"name": "project_url", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}