# Application
NEXT_PUBLIC_URL=http://localhost:3000
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>

# Database
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# Redis (for rate limiting and sessions)
REDIS_URL="redis://default:<EMAIL>:10618"

# Authentication
BETTER_AUTH_URL="http://localhost:3000"
BETTER_AUTH_SECRET="QePaovbOwgf9qgSpNgMZGCZpoi26coiJ"


NEXT_PUBLIC_ONE_TAP_CLIENT_ID="271279723990-h9cva5nloaj0g3ii564kvus9oodo61ep.apps.googleusercontent.com"
# Google OAuth
NEXT_PUBLIC_GOOGLE_CLIENT_ID="271279723990-h9cva5nloaj0g3ii564kvus9oodo61ep.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# GitHub OAuth
GITHUB_CLIENT_ID="********************"
GITHUB_CLIENT_SECRET="92efbb0e72ab74db731c17f58ccac4d072115f66"

# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
NEXT_PUBLIC_PREMIUM_PAYMENT_LINK=https://buy.stripe.com/...
NEXT_PUBLIC_PREMIUM_PLUS_PAYMENT_LINK=https://buy.stripe.com/...

# Cloudflare Turnstile (Anti-bot)
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key
TURNSTILE_SECRET_KEY=your_turnstile_secret_key

# Discord Notifications
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/...
DISCORD_LAUNCH_WEBHOOK_URL=https://discord.com/api/webhooks/...

# Email (Resend)
RESEND_API_KEY=re_...

# Cron Jobs
CRON_API_KEY=your_cron_api_key

# UploadThing
NEXT_PUBLIC_UPLOADTHING_URL="https://localhost:3000"
UPLOADTHING_TOKEN="************************************************************************************************************************************************************************"

#Plausible
PLAUSIBLE_API_KEY="sasas"
PLAUSIBLE_URL="http://localhost:3000"
PLAUSIBLE_SITE_ID="sasas"

UMAMI_URL="http://honghong-ar3otktu2-m4vvn.vercel.app"
UMAMI_USERNAME="<EMAIL>"
UMAMI_PASSWORD="tamcoine"
UMAMI_WEBSITE_ID="333eec04-bce7-4a63-a332-a89ce16f59b7"