"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"

import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

interface DatePickerProps {
  date: Date
}

export function DatePicker({ date }: DatePickerProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(date)
  const router = useRouter()

  const handleSelect = (date: Date | undefined) => {
    setSelectedDate(date)
    if (date) {
      // Format date as YYYY-MM-DD
      const formattedDate = format(date, "yyyy-MM-dd")
      router.push(`/winners?date=${formattedDate}`)
    }
  }

  return (
    <div className="flex items-center space-x-2">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "w-auto justify-start text-left font-normal",
              !selectedDate && "text-muted-foreground",
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="center">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleSelect}
            initialFocus
            disabled={{ after: new Date() }}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
