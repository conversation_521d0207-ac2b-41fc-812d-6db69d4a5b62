/**
 * Utility functions for handling image URLs safely
 */

/**
 * Get a safe logo URL that won't cause Next.js Image configuration errors
 * @param logoUrl - The original logo URL
 * @returns A safe URL that can be used with Next.js Image component
 */
export function getSafeLogoUrl(logoUrl: string | null | undefined): string {
  // If no logo URL provided, use placeholder
  if (!logoUrl) {
    return '/placeholder.svg'
  }

  // If it's a placehold.co URL, use local placeholder instead
  // This prevents Next.js Image configuration errors
  if (logoUrl.includes('placehold.co')) {
    return '/placeholder.svg'
  }

  // Return the original URL if it's safe
  return logoUrl
}

/**
 * Check if an image URL is from an external domain that needs to be configured
 * @param url - The image URL to check
 * @returns true if the URL is from an external domain
 */
export function isExternalImageUrl(url: string): boolean {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname !== 'localhost' && !url.startsWith('/')
  } catch {
    return false
  }
}

/**
 * Get the hostname from an image URL
 * @param url - The image URL
 * @returns The hostname or null if invalid
 */
export function getImageHostname(url: string): string | null {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return null
  }
}
