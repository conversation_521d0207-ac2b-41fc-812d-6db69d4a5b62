import { S3Client } from "@aws-sdk/client-s3"

/**
 * AWS S3 Client Configuration (lazy loaded)
 *
 * Tạo và cấu hình S3 client để sử dụng cho upload files
 * Sử dụng environment variables để cấu hình credentials và region
 */
let _s3Client: S3Client | null = null

export const getS3Client = () => {
  if (!_s3Client) {
    _s3Client = new S3Client({
      region: process.env.AWS_S3_REGION!,
      credentials: {
        accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY!,
      },
      // Optimized retry configuration
      maxAttempts: 5, // Increased retry attempts
      retryMode: "adaptive", // Adaptive retry mode for better performance
      // Optimized timeout và connection pooling
      requestHandler: {
        requestTimeout: 60000, // 60 seconds for large files
        connectionTimeout: 10000, // 10 seconds connection timeout
        httpsAgent: {
          maxSockets: 50, // Increased connection pool
          keepAlive: true, // Keep connections alive
          keepAliveMsecs: 30000, // 30 seconds keep alive
          maxFreeSockets: 10, // Free socket pool
        },
      },
      // Enable request compression
      useAccelerateEndpoint: false, // Can be enabled if using S3 Transfer Acceleration
      forcePathStyle: false, // Use virtual hosted-style URLs
    })
  }
  return _s3Client
}

// Backward compatibility
export const s3Client = new Proxy({} as S3Client, {
  get(target, prop) {
    return (getS3Client() as unknown as Record<string, unknown>)[prop as string]
  }
})

/**
 * S3 Configuration Constants (lazy loaded)
 */
export const getS3Config = () => ({
  BUCKET_NAME: process.env.AWS_S3_BUCKET_NAME!,
  PUBLIC_URL: process.env.AWS_S3_PUBLIC_URL!,
  REGION: process.env.AWS_S3_REGION!,
  ACCESS_KEY_ID: process.env.AWS_S3_ACCESS_KEY_ID!,
  SECRET_ACCESS_KEY: process.env.AWS_S3_SECRET_ACCESS_KEY!,
} as const)

// Backward compatibility - will be evaluated when accessed
export const S3_CONFIG = new Proxy({} as ReturnType<typeof getS3Config>, {
  get(target, prop) {
    return getS3Config()[prop as keyof ReturnType<typeof getS3Config>]
  }
})

/**
 * Validate S3 Configuration
 * Kiểm tra xem tất cả environment variables cần thiết đã được cấu hình chưa
 */
export function validateS3Config() {
  const requiredEnvVars = [
    'AWS_S3_REGION',
    'AWS_S3_BUCKET_NAME', 
    'AWS_S3_ACCESS_KEY_ID',
    'AWS_S3_SECRET_ACCESS_KEY',
    'AWS_S3_PUBLIC_URL'
  ]

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    throw new Error(
      `Missing required S3 environment variables: ${missingVars.join(', ')}`
    )
  }
}

// Validate configuration on import (skip for scripts and tests)
if (process.env.NODE_ENV !== 'test' && !process.argv[1]?.includes('scripts/')) {
  try {
    validateS3Config()
  } catch {
    // Ignore validation errors during module import
    // They will be caught when actually using the client
  }
}
