---
description:
globs:
alwaysApply: false
---
# <PERSON><PERSON> sở dữ liệu

Open-Launch sử dụng Drizzle ORM với PostgreSQL. Cấu trúc cơ sở dữ liệu được định nghĩa trong [drizzle/db/schema.ts](mdc:drizzle/db/schema.ts).

## Cấu hình cơ sở dữ liệu

- [drizzle/db/index.ts](mdc:drizzle/db/index.ts): Cấu hình kết nối cơ sở dữ liệu
- [drizzle.config.ts](mdc:drizzle.config.ts): Cấu hình Drizzle ORM

## Migrations

Các migrations được lưu trữ trong thư mục [drizzle/migrations](mdc:drizzle/migrations/). Các migrations được tạo tự động khi schema thay đổi.

## C<PERSON><PERSON> bảng chính

Schema cơ sở dữ liệu định nghĩa các bảng sau:

- `users`: <PERSON>h<PERSON><PERSON> tin người dùng
- `projects`: Thông tin dự án
- `upvotes`: L<PERSON><PERSON>t vote cho dự án
- `launches`: Thông tin về các đợt ra mắt sản phẩm
- `categories`: Danh mục dự án
- `subscriptions`: Thông tin đăng ký của người dùng

## Truy vấn cơ sở dữ liệu

Các truy vấn cơ sở dữ liệu được thực hiện trong các file actions trong thư mục [app/actions](mdc:app/actions/).
