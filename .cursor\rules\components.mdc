---
description:
globs:
alwaysApply: false
---
# <PERSON><PERSON> thống Components

Open-Launch sử dụng shadcn/ui làm thư viện UI components. Các components được tổ chức trong thư mục [components](mdc:components/).

## Cấu trúc Components

- `components/ui/`: Các components cơ bản từ shadcn/ui
- `components/layout/`: Components liên quan đến layout như header, footer, navigation
- `components/auth/`: Components liên quan đến xác thực
- `components/project/`: Components liên quan đến dự án
- `components/home/<USER>
- `components/admin/`: Components sử dụng trong trang quản trị
- `components/blog/`: Components liên quan đến blog
- `components/badges/`: Components liên quan đến badges
- `components/winners/`: Components liên quan đến người chiến thắng

## UI Components

Các UI components cơ bản được định nghĩa trong [components/ui](mdc:components/ui/). Các components này được xây dựng dựa trên shadcn/ui và được cấu hình trong [components.json](mdc:components.json).

## Layout Components

- [components/layout/nav.tsx](mdc:components/layout/nav.tsx): Thanh điều hướng chính
- [components/layout/footer.tsx](mdc:components/layout/footer.tsx): Footer
- [components/layout/user-nav.tsx](mdc:components/layout/user-nav.tsx): Điều hướng người dùng

## Project Components

- [components/project/submit-form.tsx](mdc:components/project/submit-form.tsx): Form gửi dự án
- [components/project/edit-project-form.tsx](mdc:components/project/edit-project-form.tsx): Form chỉnh sửa dự án
- [components/project/upvote-button.tsx](mdc:components/project/upvote-button.tsx): Nút upvote dự án
- [components/project/share-button.tsx](mdc:components/project/share-button.tsx): Nút chia sẻ dự án

## Theme Components

- [components/theme/theme-provider.tsx](mdc:components/theme/theme-provider.tsx): Provider cho theme
- [components/theme/theme-toggle.tsx](mdc:components/theme/theme-toggle.tsx): Nút chuyển đổi theme
