import { NextRequest, NextResponse } from "next/server"
import { takeScreenshotAndUpload, getScreenshotPreviewUrl } from "@/lib/screenshot-utils"
import { auth } from "@/lib/auth"

/**
 * POST /api/screenshot
 * Generate screenshot from website URL and upload to S3
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { url, action = "upload" } = body

    if (!url || typeof url !== "string") {
      return NextResponse.json(
        { error: "URL is required" },
        { status: 400 }
      )
    }

    // Validate URL format
    try {
      new URL(url)
    } catch {
      return NextResponse.json(
        { error: "Invalid URL format" },
        { status: 400 }
      )
    }

    // Handle different actions
    if (action === "preview") {
      // Return preview URL without uploading
      const previewUrl = getScreenshotPreviewUrl(url)
      
      if (!previewUrl) {
        return NextResponse.json(
          { error: "Failed to generate preview URL" },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        previewUrl,
        message: "Preview URL generated successfully"
      })
    }

    if (action === "upload") {
      // Take screenshot and upload to S3
      const result = await takeScreenshotAndUpload(url)

      if (!result.success) {
        return NextResponse.json(
          { 
            error: result.error || "Screenshot generation failed",
            details: result.details
          },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        imageUrl: result.imageUrl,
        message: "Screenshot generated and uploaded successfully"
      })
    }

    return NextResponse.json(
      { error: "Invalid action. Use 'preview' or 'upload'" },
      { status: 400 }
    )

  } catch (error) {
    console.error("Screenshot API error:", error)
    
    return NextResponse.json(
      { 
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/screenshot?url=...&action=preview
 * Get screenshot preview URL (for quick previews)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const url = searchParams.get("url")
    const action = searchParams.get("action") || "preview"

    if (!url) {
      return NextResponse.json(
        { error: "URL parameter is required" },
        { status: 400 }
      )
    }

    // Validate URL format
    try {
      new URL(url)
    } catch {
      return NextResponse.json(
        { error: "Invalid URL format" },
        { status: 400 }
      )
    }

    if (action === "preview") {
      const previewUrl = getScreenshotPreviewUrl(url)
      
      if (!previewUrl) {
        return NextResponse.json(
          { error: "Failed to generate preview URL" },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        previewUrl,
        message: "Preview URL generated successfully"
      })
    }

    return NextResponse.json(
      { error: "Only 'preview' action is supported for GET requests" },
      { status: 400 }
    )

  } catch (error) {
    console.error("Screenshot preview API error:", error)
    
    return NextResponse.json(
      { 
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    )
  }
}
