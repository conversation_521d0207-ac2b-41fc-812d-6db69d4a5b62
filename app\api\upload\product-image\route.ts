import { NextRequest, NextResponse } from "next/server"
import { headers } from "next/headers"

import { auth } from "@/lib/auth"
import { uploadToS3 } from "@/lib/s3-upload"
import { validateImageFile, generateFileKey } from "@/lib/file-validation"

/**
 * Product Image Upload API Route
 * 
 * Thay thế UploadThing projectProductImage endpoint
 * POST /api/upload/product-image
 */
export async function POST(request: NextRequest) {
  try {
    // 1. Authentication - Kiểm tra user đã đăng nhập
    const session = await auth.api.getSession({ headers: await headers() })
    const user = session?.user

    if (!user?.id) {
      return NextResponse.json(
        { 
          success: false,
          error: "Unauthorized",
          message: "You must be logged in to upload files"
        },
        { status: 401 }
      )
    }

    // 2. Get form data
    const formData = await request.formData()
    const file = formData.get("file") as File

    if (!file) {
      return NextResponse.json(
        { 
          success: false,
          error: "No file provided",
          message: "Please select a file to upload"
        },
        { status: 400 }
      )
    }

    // 3. Validate file (sử dụng 'product-image' type)
    const validationError = validateImageFile(file, 'product-image')
    if (validationError) {
      return NextResponse.json(
        { 
          success: false,
          error: validationError.code,
          message: validationError.message,
          details: validationError.details
        },
        { status: 400 }
      )
    }

    // 4. Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // 5. Generate S3 key
    const key = generateFileKey(user.id, 'product-image', file)

    // 6. Upload to S3
    const uploadResult = await uploadToS3(buffer, key, file.type)

    // 7. Log successful upload (tương tự UploadThing)
    console.log("Product Image Upload complete for userId:", user.id)
    console.log("file url", uploadResult.url)

    // 8. Return success response (tương thích với UploadThing format)
    return NextResponse.json({
      success: true,
      data: {
        url: uploadResult.url,
        key: uploadResult.key,
        size: uploadResult.size,
        contentType: uploadResult.contentType,
        uploadedBy: user.id,
        // Tương thích với UploadThing response format
        fileUrl: uploadResult.url,
      }
    })

  } catch (error) {
    console.error("Product image upload error:", error)

    // Handle different types of errors
    if (error instanceof Error) {
      // S3 upload errors
      if (error.name === 'S3UploadError') {
        return NextResponse.json(
          { 
            success: false,
            error: "Upload failed",
            message: error.message
          },
          { status: 500 }
        )
      }

      // File processing errors
      if (error.message.includes('arrayBuffer')) {
        return NextResponse.json(
          { 
            success: false,
            error: "Invalid file",
            message: "Unable to process the uploaded file"
          },
          { status: 400 }
        )
      }

      // File validation errors
      if (error.message.includes('validation')) {
        return NextResponse.json(
          { 
            success: false,
            error: "Validation failed",
            message: error.message
          },
          { status: 400 }
        )
      }
    }

    // Generic error response
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        message: "An unexpected error occurred during upload"
      },
      { status: 500 }
    )
  }
}

/**
 * Handle OPTIONS request for CORS
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}

/**
 * Handle GET request để kiểm tra endpoint status
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    message: "Product Image Upload API is ready",
    endpoint: "/api/upload/product-image",
    methods: ["POST"],
    maxFileSize: "1MB",
    allowedTypes: ["image/jpeg", "image/png", "image/webp", "image/gif"],
    recommendedDimensions: "800x450 (16:9 aspect ratio)"
  })
}
