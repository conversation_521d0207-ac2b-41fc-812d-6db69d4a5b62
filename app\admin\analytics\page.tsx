import { RiBarChartLine } from "@remixicon/react"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function AdminAnalyticsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <RiBarChartLine className="h-6 w-6" />
        <h1 className="text-2xl font-bold">Analytics</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Thống kê & Báo cáo</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Trang thống kê và báo cáo đang được phát triển...
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
