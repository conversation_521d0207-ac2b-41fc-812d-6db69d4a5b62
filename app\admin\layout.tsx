import { headers } from "next/headers"
import { redirect } from "next/navigation"

import { auth } from "@/lib/auth"
import { AdminNav } from "@/components/admin/admin-nav"
import { AdminBreadcrumb } from "@/components/admin/admin-breadcrumb"

export default async function AdminLayout({ children }: { children: React.ReactNode }) {
  // Vérifier si l'utilisateur est connecté et est admin
  const session = await auth.api.getSession({
    headers: await headers(),
  })

  if (!session?.user || session?.user.role !== "admin") {
    // Rediriger vers la page d'accueil si l'utilisateur n'est pas un administrateur
    redirect("/")
  }

  return (
    <div className="min-h-screen bg-background">
      <AdminNav />
      <main className="flex-1">
        <div className="container mx-auto max-w-6xl p-4 md:p-6 lg:p-8">
          <AdminBreadcrumb />
          {children}
        </div>
      </main>
    </div>
  )
}
