"use client"

import { useState, useCallback } from "react"

interface ScreenshotState {
  loading: boolean
  error: string | null
  imageUrl: string | null
  previewUrl: string | null
}

interface ScreenshotResponse {
  success: boolean
  imageUrl?: string
  previewUrl?: string
  error?: string
  details?: string
  message?: string
}

interface UseScreenshotReturn {
  state: ScreenshotState
  generateScreenshot: (url: string) => Promise<string | null>
  getPreview: (url: string) => Promise<string | null>
  reset: () => void
}

/**
 * Custom hook for screenshot generation using ScreenshotOne API
 */
export function useScreenshot(): UseScreenshotReturn {
  const [state, setState] = useState<ScreenshotState>({
    loading: false,
    error: null,
    imageUrl: null,
    previewUrl: null,
  })

  const reset = useCallback(() => {
    setState({
      loading: false,
      error: null,
      imageUrl: null,
      previewUrl: null,
    })
  }, [])

  const generateScreenshot = useCallback(async (url: string): Promise<string | null> => {
    if (!url.trim()) {
      setState(prev => ({ ...prev, error: "URL is required" }))
      return null
    }

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const response = await fetch("/api/screenshot", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: url.trim(),
          action: "upload",
        }),
      })

      const data: ScreenshotResponse = await response.json()

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`)
      }

      if (!data.success || !data.imageUrl) {
        throw new Error(data.error || "Failed to generate screenshot")
      }

      setState(prev => ({
        ...prev,
        loading: false,
        imageUrl: data.imageUrl!,
        error: null,
      }))

      return data.imageUrl

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred"
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }))

      console.error("Screenshot generation failed:", error)
      return null
    }
  }, [])

  const getPreview = useCallback(async (url: string): Promise<string | null> => {
    if (!url.trim()) {
      setState(prev => ({ ...prev, error: "URL is required" }))
      return null
    }

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const response = await fetch(`/api/screenshot?url=${encodeURIComponent(url.trim())}&action=preview`, {
        method: "GET",
      })

      const data: ScreenshotResponse = await response.json()

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`)
      }

      if (!data.success || !data.previewUrl) {
        throw new Error(data.error || "Failed to generate preview")
      }

      setState(prev => ({
        ...prev,
        loading: false,
        previewUrl: data.previewUrl!,
        error: null,
      }))

      return data.previewUrl

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred"
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }))

      console.error("Screenshot preview failed:", error)
      return null
    }
  }, [])

  return {
    state,
    generateScreenshot,
    getPreview,
    reset,
  }
}

/**
 * Utility hook for batch screenshot generation
 */
export function useBatchScreenshot() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<Array<{ url: string; imageUrl: string | null; error: string | null }>>([])

  const generateBatch = useCallback(async (urls: string[]) => {
    setLoading(true)
    setResults([])

    const batchResults = await Promise.allSettled(
      urls.map(async (url) => {
        try {
          const response = await fetch("/api/screenshot", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              url: url.trim(),
              action: "upload",
            }),
          })

          const data: ScreenshotResponse = await response.json()

          if (!response.ok || !data.success) {
            throw new Error(data.error || "Failed to generate screenshot")
          }

          return {
            url,
            imageUrl: data.imageUrl || null,
            error: null,
          }
        } catch (error) {
          return {
            url,
            imageUrl: null,
            error: error instanceof Error ? error.message : "Unknown error",
          }
        }
      })
    )

    const finalResults = batchResults.map((result, index) => {
      if (result.status === "fulfilled") {
        return result.value
      } else {
        return {
          url: urls[index],
          imageUrl: null,
          error: "Processing failed",
        }
      }
    })

    setResults(finalResults)
    setLoading(false)

    return finalResults
  }, [])

  return {
    loading,
    results,
    generateBatch,
  }
}

/**
 * Simple utility function to validate URLs
 */
export function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}
