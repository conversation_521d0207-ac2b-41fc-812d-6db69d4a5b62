"use client"

import { useState } from "react"
import { use<PERSON>outer } from "next/navigation"
import Link from "next/link"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"

import {
  RiArrowLeftLine,
  RiSaveLine,
  RiLoader4Line,
} from "@remixicon/react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { updateAdminProject } from "@/app/actions/admin"
import { toast } from "sonner"

interface Project {
  id: string
  name: string
  slug: string
  description: string
  websiteUrl: string
  logoUrl: string
  coverImageUrl: string | null
  productImage: string | null
  githubUrl: string | null
  twitterUrl: string | null
  techStack: string[] | null
  pricing: string
  platforms: string[] | null
  launchStatus: string
  scheduledLaunchDate: Date | null
  launchType: string | null
  featuredOnHomepage: boolean | null
  dailyRanking: number | null
  categories: { id: string; name: string }[]
}

interface Category {
  id: string
  name: string
}

interface AdminProjectEditFormProps {
  project: Project
  categories: Category[]
}

const formSchema = z.object({
  name: z.string().min(1, "Tên project là bắt buộc"),
  description: z.string().min(1, "Mô tả là bắt buộc"),
  websiteUrl: z.string().url("URL không hợp lệ"),
  logoUrl: z.string().url("URL logo không hợp lệ"),
  coverImageUrl: z.string().url("URL cover image không hợp lệ").optional().or(z.literal("")),
  productImage: z.string().url("URL product image không hợp lệ").optional().or(z.literal("")),
  githubUrl: z.string().url("URL GitHub không hợp lệ").optional().or(z.literal("")),
  twitterUrl: z.string().url("URL Twitter không hợp lệ").optional().or(z.literal("")),
  pricing: z.string().min(1, "Pricing là bắt buộc"),
  launchStatus: z.string().min(1, "Launch status là bắt buộc"),
  launchType: z.string().optional(),
  featuredOnHomepage: z.boolean(),
  categories: z.array(z.string()).min(1, "Ít nhất một category là bắt buộc"),
  techStack: z.array(z.string()),
  platforms: z.array(z.string()),
})

type FormData = z.infer<typeof formSchema>

const statusOptions = [
  { value: "scheduled", label: "Đã lên lịch" },
  { value: "launched", label: "Đã ra mắt" },
  { value: "draft", label: "Bản nháp" },
  { value: "cancelled", label: "Đã hủy" },
]

const launchTypeOptions = [
  { value: "free", label: "Miễn phí" },
  { value: "premium", label: "Premium" },
  { value: "premium_plus", label: "Premium Plus" },
]

const pricingOptions = [
  { value: "FREE", label: "Miễn phí" },
  { value: "FREEMIUM", label: "Freemium" },
  { value: "PAID", label: "Trả phí" },
]

const techStackOptions = [
  "React", "Vue", "Angular", "Next.js", "Nuxt.js", "Svelte",
  "Node.js", "Express", "Fastify", "NestJS",
  "Python", "Django", "Flask", "FastAPI",
  "TypeScript", "JavaScript", "PHP", "Laravel",
  "Go", "Rust", "Java", "Spring Boot",
  "PostgreSQL", "MySQL", "MongoDB", "Redis",
  "Docker", "Kubernetes", "AWS", "Vercel", "Netlify"
]

const platformOptions = [
  "Web", "iOS", "Android", "Desktop", "Chrome Extension",
  "Firefox Extension", "Safari Extension", "VS Code Extension",
  "Figma Plugin", "Slack App", "Discord Bot", "Telegram Bot"
]

export function AdminProjectEditForm({ project, categories }: AdminProjectEditFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: project.name,
      description: project.description,
      websiteUrl: project.websiteUrl,
      logoUrl: project.logoUrl,
      coverImageUrl: project.coverImageUrl || "",
      productImage: project.productImage || "",
      githubUrl: project.githubUrl || "",
      twitterUrl: project.twitterUrl || "",
      pricing: project.pricing,
      launchStatus: project.launchStatus,
      launchType: project.launchType || "",
      featuredOnHomepage: project.featuredOnHomepage ?? false,
      categories: project.categories.map(c => c.id),
      techStack: project.techStack || [],
      platforms: project.platforms || [],
    },
  })

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    try {
      const result = await updateAdminProject(project.id, {
        name: data.name,
        description: data.description,
        websiteUrl: data.websiteUrl,
        logoUrl: data.logoUrl,
        coverImageUrl: data.coverImageUrl || null,
        productImage: data.productImage || null,
        githubUrl: data.githubUrl || null,
        twitterUrl: data.twitterUrl || null,
        pricing: data.pricing,
        launchStatus: data.launchStatus,
        launchType: data.launchType || undefined,
        featuredOnHomepage: data.featuredOnHomepage,
        categories: data.categories,
        techStack: data.techStack,
        platforms: data.platforms,
      })

      if (result.success) {
        toast.success("Đã cập nhật project thành công")
        router.push(`/admin/projects/${project.id}`)
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi cập nhật project")
      }
    } catch {
      toast.error("Có lỗi xảy ra khi cập nhật project")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/projects/${project.id}`}>
              <RiArrowLeftLine className="mr-2 h-4 w-4" />
              Quay lại
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Chỉnh sửa Project</h1>
            <p className="text-muted-foreground">{project.name}</p>
          </div>
        </div>

        <Button
          type="submit"
          form="edit-project-form"
          disabled={isSubmitting}
          className="gap-2"
        >
          {isSubmitting ? (
            <RiLoader4Line className="h-4 w-4 animate-spin" />
          ) : (
            <RiSaveLine className="h-4 w-4" />
          )}
          {isSubmitting ? "Đang lưu..." : "Lưu thay đổi"}
        </Button>
      </div>

      <Form {...form}>
        <form id="edit-project-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Thông tin cơ bản</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tên project *</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mô tả *</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={3} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="websiteUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website URL *</FormLabel>
                      <FormControl>
                        <Input {...field} type="url" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="logoUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Logo URL *</FormLabel>
                      <FormControl>
                        <Input {...field} type="url" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Status & Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Trạng thái & Cài đặt</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="launchStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Launch Status *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {statusOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="launchType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Launch Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Chọn launch type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {launchTypeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="pricing"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pricing *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {pricingOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="featuredOnHomepage"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Featured trên homepage</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </div>

          {/* Additional URLs */}
          <Card>
            <CardHeader>
              <CardTitle>URLs bổ sung</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="coverImageUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cover Image URL</FormLabel>
                    <FormControl>
                      <Input {...field} type="url" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="productImage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Product Image URL</FormLabel>
                    <FormControl>
                      <Input {...field} type="url" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="githubUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>GitHub URL</FormLabel>
                    <FormControl>
                      <Input {...field} type="url" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="twitterUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Twitter URL</FormLabel>
                    <FormControl>
                      <Input {...field} type="url" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Categories *</CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="categories"
                render={() => (
                  <FormItem>
                    <div className="grid gap-3 sm:grid-cols-2 md:grid-cols-3">
                      {categories.map((category) => (
                        <FormField
                          key={category.id}
                          control={form.control}
                          name="categories"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={category.id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(category.id)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, category.id])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== category.id
                                            )
                                          )
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {category.name}
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Tech Stack */}
          <Card>
            <CardHeader>
              <CardTitle>Tech Stack</CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="techStack"
                render={() => (
                  <FormItem>
                    <div className="grid gap-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                      {techStackOptions.map((tech) => (
                        <FormField
                          key={tech}
                          control={form.control}
                          name="techStack"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={tech}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(tech)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, tech])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== tech
                                            )
                                          )
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal text-sm">
                                  {tech}
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Platforms */}
          <Card>
            <CardHeader>
              <CardTitle>Platforms</CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="platforms"
                render={() => (
                  <FormItem>
                    <div className="grid gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                      {platformOptions.map((platform) => (
                        <FormField
                          key={platform}
                          control={form.control}
                          name="platforms"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={platform}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(platform)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, platform])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== platform
                                            )
                                          )
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {platform}
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        </form>
      </Form>
    </div>
  )
}
