# Application
NEXT_PUBLIC_URL=http://localhost:3000
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/open_launch

# Redis (for rate limiting and sessions)
REDIS_URL=redis://localhost:6379

# Authentication
BETTER_AUTH_URL=http://localhost:3000
BETTER_AUTH_SECRET=

# Google OAuth
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
NEXT_PUBLIC_PREMIUM_PAYMENT_LINK=https://buy.stripe.com/...
NEXT_PUBLIC_PREMIUM_PLUS_PAYMENT_LINK=https://buy.stripe.com/...

# Cloudflare Turnstile (Anti-bot)
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key
TURNSTILE_SECRET_KEY=your_turnstile_secret_key

# Discord Notifications
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/...
DISCORD_LAUNCH_WEBHOOK_URL=https://discord.com/api/webhooks/...

# Email (Resend)
RESEND_API_KEY=re_...

# Cron Jobs
CRON_API_KEY=your_cron_api_key

# AWS S3 Configuration
AWS_S3_REGION=ap-southeast-1
AWS_S3_BUCKET_NAME=open-launch-uploads
AWS_S3_ACCESS_KEY_ID=your_aws_access_key_id
AWS_S3_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_PUBLIC_URL=https://your-bucket.s3.ap-southeast-1.amazonaws.com

# ScreenshotOne API (for automatic product images)
SCREENSHOTONE_ACCESS_KEY=your_screenshotone_access_key

#Plausible
PLAUSIBLE_API_KEY=
PLAUSIBLE_URL=
PLAUSIBLE_SITE_ID=