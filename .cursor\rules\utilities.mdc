---
description:
globs:
alwaysApply: false
---
# Tiện ích và T<PERSON> viện

Open-Launch sử dụng nhiều tiện ích và thư viện để xây dựng ứng dụng. Các tiện ích được tổ chức trong thư mục [lib](mdc:lib/).

## Tiện ích chính

- [lib/utils.ts](mdc:lib/utils.ts): <PERSON><PERSON><PERSON> hàm tiện ích chung
- [lib/constants.ts](mdc:lib/constants.ts): Các hằng số được sử dụng trong ứng dụng
- [lib/email.ts](mdc:lib/email.ts): Tiện ích gửi email
- [lib/rate-limit.ts](mdc:lib/rate-limit.ts): Giới hạn tốc độ yêu cầu
- [lib/image-utils.ts](mdc:lib/image-utils.ts): Tiện ích xử lý hình ảnh
- [lib/link-utils.ts](mdc:lib/link-utils.ts): Tiện ích xử lý liên kết
- [lib/transactional-emails.ts](mdc:lib/transactional-emails.ts): Mẫu email giao dịch
- [lib/uploadthing.ts](mdc:lib/uploadthing.ts): Cấu hình UploadThing cho tải lên tệp
- [lib/discord-notification.ts](mdc:lib/discord-notification.ts): Tiện ích gửi thông báo Discord

## Hooks

- [hooks/use-mobile.ts](mdc:hooks/use-mobile.ts): Hook kiểm tra thiết bị di động
- [lib/hooks/use-debounce.ts](mdc:lib/hooks/use-debounce.ts): Hook debounce
- [lib/hooks/use-search.ts](mdc:lib/hooks/use-search.ts): Hook tìm kiếm

## Validations

- [lib/validations/auth.ts](mdc:lib/validations/auth.ts): Schema validation cho xác thực

## Thư viện chính

- **Next.js**: Framework React
- **Drizzle ORM**: ORM cho cơ sở dữ liệu
- **NextAuth.js**: Xác thực
- **shadcn/ui**: UI components
- **Tailwind CSS**: Framework CSS
- **UploadThing**: Tải lên tệp
- **Zod**: Validation schema
