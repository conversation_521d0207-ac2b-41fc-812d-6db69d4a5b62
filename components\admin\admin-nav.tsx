"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

import {
  RiDashboardLine,
  RiRocketLine,
  RiUserLine,
  RiSettings3Line,
  RiBarChartLine,
  RiMenuLine,
} from "@remixicon/react"
import { Tag } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet"

const navigation = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: RiDashboardLine,
    description: "Tổng quan hệ thống",
  },
  {
    name: "Projects",
    href: "/admin/projects",
    icon: RiRocketLine,
    description: "Quản lý dự án",
    badge: "New",
  },
  {
    name: "Users",
    href: "/admin/users",
    icon: RiUserLine,
    description: "Qu<PERSON>n lý người dùng",
  },
  {
    name: "Categories",
    href: "/admin/categories",
    icon: Tag,
    description: "Quản lý danh mục",
  },
  {
    name: "Analytics",
    href: "/admin/analytics",
    icon: RiBarChartLine,
    description: "Thống kê & báo cáo",
  },
  {
    name: "Settings",
    href: "/admin/settings",
    icon: RiSettings3Line,
    description: "Cài đặt hệ thống",
  },
]

function AdminNavMenu() {
  const pathname = usePathname()

  return (
    <NavigationMenu className="hidden md:flex">
      <NavigationMenuList className="gap-1">
        {navigation.map((item) => {
          const isActive = pathname === item.href || (item.href !== "/admin" && pathname.startsWith(item.href))

          return (
            <NavigationMenuItem key={item.name}>
              <NavigationMenuLink asChild>
                <Link
                  href={item.href}
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "h-9 px-3 text-sm gap-2",
                    isActive && "bg-primary text-primary-foreground"
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  <span>{item.name}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="text-xs ml-1">
                      {item.badge}
                    </Badge>
                  )}
                </Link>
              </NavigationMenuLink>
            </NavigationMenuItem>
          )
        })}
      </NavigationMenuList>
    </NavigationMenu>
  )
}

interface AdminNavProps {
  className?: string
}

export function AdminNav({ className }: AdminNavProps) {
  return (
    <nav className={cn("bg-background/95 border-border/40 border-b backdrop-blur-sm", className)}>
      <div className="container mx-auto max-w-6xl px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center gap-8">
            <Link href="/admin" className="font-heading flex items-center">
              <span className="font-heading flex items-center text-lg font-bold">
                <div className="flex h-6 w-6 items-center justify-center rounded-lg bg-primary text-primary-foreground mr-1">
                  <RiSettings3Line className="h-4 w-4" />
                </div>
                Admin Panel
              </span>
            </Link>

            {/* Navigation principale */}
            <AdminNavMenu />
          </div>

          {/* Mobile Navigation */}
          <div className="flex items-center md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="h-9 w-9">
                  <RiMenuLine className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right">
                <div className="flex h-full flex-col">
                  <div className="px-2">
                    <SheetHeader className="mb-2 pb-0">
                      <SheetTitle>Admin Menu</SheetTitle>
                    </SheetHeader>
                  </div>

                  <div className="flex-1 overflow-y-auto">
                    <div className="mb-4">
                      <div className="mb-2 px-6">
                        <h3 className="text-muted-foreground mb-2 text-xs font-medium">
                          ADMIN NAVIGATION
                        </h3>
                      </div>
                      <div className="space-y-1">
                        {navigation.map((item) => (
                          <SheetClose asChild key={item.name}>
                            <Link
                              href={item.href}
                              className="hover:bg-muted/50 flex items-center gap-3 px-6 py-2.5 text-sm transition-colors"
                            >
                              <item.icon className="text-muted-foreground h-4 w-4" />
                              <span className="flex-1">{item.name}</span>
                              {item.badge && (
                                <Badge variant="secondary" className="text-xs">
                                  {item.badge}
                                </Badge>
                              )}
                            </Link>
                          </SheetClose>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  )
}
