"use server"

// Add auth function
async function getUmamiToken() {
  const UMAMI_URL = process.env.UMAMI_URL;
  const UMAMI_USERNAME = process.env.UMAMI_USERNAME;
  const UMAMI_PASSWORD = process.env.UMAMI_PASSWORD;

  if (!UMAMI_URL || !UMAMI_USERNAME || !UMAMI_PASSWORD) {
    console.warn("Umami credentials not configured. Analytics data will not be available.");
    return null;
  }

  try {
    const response = await fetch(`${UMAMI_URL}/api/auth/login`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ username: UMAMI_USERNAME, password: UMAMI_PASSWORD }),
    });

    if (!response.ok) {
      console.warn(`Umami authentication failed with status: ${response.status}`);
      return null;
    }

    const { token } = await response.json();
    return token;
  } catch (error) {
    console.warn("Umami auth error:", error);
    return null;
  }
}

// Rewrite getLast24hVisitors using Umami API
export async function getLast24hVisitors(): Promise<number | null> {
  const token = await getUmamiToken();
  if (!token) return 0; // Return 0 instead of null for better UX

  const UMAMI_URL = process.env.UMAMI_URL;
  const UMAMI_WEBSITE_ID = process.env.UMAMI_WEBSITE_ID;

  if (!UMAMI_URL || !UMAMI_WEBSITE_ID) return 0;

  const now = new Date();
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  try {
    const response = await fetch(`${UMAMI_URL}/api/websites/${UMAMI_WEBSITE_ID}/stats?startAt=${yesterday.getTime()}&endAt=${now.getTime()}&unit=day&metrics=visitors`, {
      headers: { Authorization: `Bearer ${token}` },
      next: { revalidate: 600 },
    });

    if (!response.ok) return 0;

    const data = await response.json();
    return data.visitors.value || 0;
  } catch (error) {
    console.warn("Umami API error:", error);
    return 0;
  }
}

// Similarly rewrite other functions: getLast7DaysVisitors, getLast30DaysVisitors, getLast30DaysPageviews
// Use appropriate date ranges, e.g., for 7d: startAt = now - 7 days, metrics=visitors or pageviews

export async function getLast7DaysVisitors(): Promise<number | null> {
  const token = await getUmamiToken();
  if (!token) return 0;

  const UMAMI_URL = process.env.UMAMI_URL;
  const UMAMI_WEBSITE_ID = process.env.UMAMI_WEBSITE_ID;

  if (!UMAMI_URL || !UMAMI_WEBSITE_ID) return 0;

  const now = new Date();
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  try {
    const response = await fetch(`${UMAMI_URL}/api/websites/${UMAMI_WEBSITE_ID}/stats?startAt=${sevenDaysAgo.getTime()}&endAt=${now.getTime()}&unit=day&metrics=visitors`, {
      headers: { Authorization: `Bearer ${token}` },
      next: { revalidate: 3600 },
    });

    if (!response.ok) return 0;

    const data = await response.json();
    return data.visitors.value || 0;
  } catch (error) {
    console.warn("Umami API error (7 days):", error);
    return 0;
  }
}

export async function getLast30DaysVisitors(): Promise<number | null> {
  const token = await getUmamiToken();
  if (!token) return 0;

  const UMAMI_URL = process.env.UMAMI_URL;
  const UMAMI_WEBSITE_ID = process.env.UMAMI_WEBSITE_ID;

  if (!UMAMI_URL || !UMAMI_WEBSITE_ID) return 0;

  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  try {
    const response = await fetch(`${UMAMI_URL}/api/websites/${UMAMI_WEBSITE_ID}/stats?startAt=${thirtyDaysAgo.getTime()}&endAt=${now.getTime()}&unit=day&metrics=visitors`, {
      headers: { Authorization: `Bearer ${token}` },
      next: { revalidate: 21600 },
    });

    if (!response.ok) return 0;

    const data = await response.json();
    return data.visitors.value || 0;
  } catch (error) {
    console.warn("Umami API error (30 days):", error);
    return 0;
  }
}

export async function getLast30DaysPageviews(): Promise<number | null> {
  const token = await getUmamiToken();
  if (!token) return 0;

  const UMAMI_URL = process.env.UMAMI_URL;
  const UMAMI_WEBSITE_ID = process.env.UMAMI_WEBSITE_ID;

  if (!UMAMI_URL || !UMAMI_WEBSITE_ID) return 0;

  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  try {
    const response = await fetch(`${UMAMI_URL}/api/websites/${UMAMI_WEBSITE_ID}/stats?startAt=${thirtyDaysAgo.getTime()}&endAt=${now.getTime()}&unit=day&metrics=pageviews`, {
      headers: { Authorization: `Bearer ${token}` },
      next: { revalidate: 21600 },
    });

    if (!response.ok) return 0;

    const data = await response.json();
    return data.pageviews.value || 0;
  } catch (error) {
    console.warn("Umami API error (30 days pageviews):", error);
    return 0;
  }
}
