

## 📋 Phân Tích Tính <PERSON>ăng Upload trong Open-Launch

Dựa trên việc phân tích mã nguồn, tô<PERSON> sẽ trình bày chi tiết về tính năng upload được triển khai trong dự án Open-Launch.

### 🔧 **1. <PERSON><PERSON><PERSON>ệ Sử Dụng**

Dự án sử dụng **UploadThing** làm giải pháp chính cho tính năng upload file:

````json path=package.json mode=EXCERPT
"@uploadthing/react": "^7.3.1",
"uploadthing": "^7.7.2",
````

### 🏗️ **2. <PERSON>ấu Trúc Tính Năng Upload**

#### **2.1 Cấu hình API Core**
File chính định nghĩa các endpoint upload:

````typescript path=app/api/uploadthing/core.ts mode=EXCERPT
export const ourFileRouter = {
  projectLogo: f({ image: { maxFileSize: "1MB", maxFileCount: 1 } })
    .middleware(authenticateUser)
    .onUploadComplete(async ({ metadata, file }) => {
      console.log("Logo Upload complete for userId:", metadata.userId)
      console.log("file url", file.ufsUrl)
      return { uploadedBy: metadata.userId, fileUrl: file.ufsUrl }
    }),

  projectProductImage: f({ image: { maxFileSize: "1MB", maxFileCount: 1 } })
    .middleware(authenticateUser)
    .onUploadComplete(async ({ metadata, file }) => {
      console.log("Product Image Upload complete for userId:", metadata.userId)
      console.log("file url", file.ufsUrl)
      return { uploadedBy: metadata.userId, fileUrl: file.ufsUrl }
    }),
} satisfies FileRouter
````

#### **2.2 API Routes**
````typescript path=app/api/uploadthing/route.ts mode=EXCERPT
import { createRouteHandler } from "uploadthing/next"
import { ourFileRouter } from "./core"

export const { GET, POST } = createRouteHandler({
  router: ourFileRouter,
})
````

#### **2.3 Components Upload**
````typescript path=lib/uploadthing.ts mode=EXCERPT
import { generateUploadButton, generateUploadDropzone } from "@uploadthing/react"
import type { OurFileRouter } from "@/app/api/uploadthing/core"

export const UploadButton = generateUploadButton<OurFileRouter>()
export const UploadDropzone = generateUploadDropzone<OurFileRouter>()
````

### 📊 **3. Database Schema**

Các trường lưu trữ URL file upload trong database:

````typescript path=drizzle/db/schema.ts mode=EXCERPT
export const project = pgTable("project", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  logoUrl: text("logo_url").notNull(),
  coverImageUrl: text("cover_image_url"),
  productImage: text("product_image"),
  // ... các trường khác
})
````

### 🎯 **4. Các Loại Upload Được Hỗ Trợ**

#### **4.1 Project Logo**
- **Kích thước tối đa**: 1MB
- **Số lượng file**: 1 file
- **Định dạng**: Image
- **Khuyến nghị**: Tỷ lệ 1:1 (256x256px)

#### **4.2 Product Image**
- **Kích thước tối đa**: 1MB  
- **Số lượng file**: 1 file
- **Định dạng**: Image
- **Khuyến nghị**: Tỷ lệ 16:9 (800x450px)

### 🔐 **5. Bảo Mật**

#### **5.1 Xác Thực Người Dùng**
````typescript path=app/api/uploadthing/core.ts mode=EXCERPT
const authenticateUser = async () => {
  const session = await auth.api.getSession({ headers: await headers() })
  const user = session?.user

  if (!user?.id) throw new UploadThingError("Unauthorized")

  return { userId: user.id }
}
````

#### **5.2 Middleware Bảo Mật**
- Tất cả endpoint upload đều yêu cầu xác thực
- Kiểm tra session người dùng trước khi cho phép upload
- Trả về lỗi "Unauthorized" nếu không có quyền

### ⚙️ **6. Cấu Hình Environment**

````bash path=.env.example mode=EXCERPT
# UploadThing
NEXT_PUBLIC_UPLOADTHING_URL=your_uploadthing_url_without_protocol
UPLOADTHING_TOKEN=your_uploadthing_token
````

### 🎨 **7. Styling và UI**

#### **7.1 CSS Import**
````css path=app/globals.css mode=EXCERPT
@import "uploadthing/tw/v4";
@source "../node_modules/@uploadthing/react/dist";
````

#### **7.2 Next.js Image Configuration**
````typescript path=next.config.ts mode=EXCERPT
images: {
  remotePatterns: [
    {
      protocol: "https",
      hostname: process.env.NEXT_PUBLIC_UPLOADTHING_URL!,
    },
    {
      protocol: "https",
      hostname: "yxucdfr9f5.ufs.sh",
      pathname: "/**",
    },
  ],
}
````

### 🔄 **8. Quy Trình Upload**

#### **8.1 Quy Trình Logo Upload**
1. Người dùng click nút "Upload Logo"
2. Kiểm tra xác thực qua middleware
3. Upload file lên UploadThing
4. Nhận URL file từ server
5. Lưu URL vào state component
6. Hiển thị preview image
7. Lưu URL vào database khi submit form

#### **8.2 Xử Lý Lỗi**
- Hiển thị loading state khi đang upload
- Xử lý lỗi upload và hiển thị thông báo
- Cho phép xóa và upload lại

### 📱 **9. User Experience**

#### **9.1 Features UX**
- **Loading State**: Hiển thị spinner khi đang upload
- **Preview**: Xem trước ảnh sau khi upload thành công
- **Remove**: Nút xóa ảnh đã upload
- **Error Handling**: Thông báo lỗi chi tiết
- **Validation**: Kiểm tra kích thước và định dạng file

#### **9.2 Responsive Design**
- Tương thích với các thiết bị khác nhau
- Sử dụng Tailwind CSS cho styling
- Tích hợp với design system của dự án

### 🚀 **10. Tối Ưu Hóa**

#### **10.1 Performance**
- Giới hạn kích thước file (1MB)
- Chỉ cho phép upload 1 file mỗi lần
- Sử dụng CDN của UploadThing

#### **10.2 SEO và Accessibility**
- Alt text cho images
- Proper ARIA labels
- Keyboard navigation support

### 📝 **11. Kết Luận**

Tính năng upload trong Open-Launch được thiết kế:
- **Đơn giản**: Chỉ 2 loại upload chính (logo và product image)
- **Bảo mật**: Xác thực người dùng bắt buộc
- **Hiệu quả**: Sử dụng UploadThing service chuyên nghiệp
- **User-friendly**: UX tốt với loading states và error handling
- **Scalable**: Dễ dàng mở rộng thêm các loại upload khác

Đây là một implementation tốt cho tính năng upload trong một ứng dụng web hiện đại, tuân thủ các best practices về bảo mật và user experience.
