"use client"

import { launchStatus as launchStatusEnum } from "@/drizzle/db/schema"
import { RiThumbUpLine } from "@remixicon/react"

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { UpvoteButton } from "@/components/project/upvote-button"

interface ProjectCardButtonsProps {
  projectId: string
  upvoteCount: number
  isAuthenticated: boolean
  hasUpvoted: boolean
  launchStatus: string
}

export function ProjectCardButtons({
  projectId,
  upvoteCount,
  isAuthenticated,
  hasUpvoted,
  launchStatus,
}: ProjectCardButtonsProps) {
  const isActiveLaunch = launchStatus === launchStatusEnum.ONGOING

  return (
    <div className="flex flex-col items-end gap-2 sm:flex-row sm:items-start">
      {isActiveLaunch ? (
        <UpvoteButton
          projectId={projectId}
          initialUpvoted={hasUpvoted}
          upvoteCount={upvoteCount}
          isAuthenticated={isAuthenticated}
          variant="compact"
        />
      ) : (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex h-12 w-12 flex-col items-center justify-center rounded-xl border-2 border-dashed">
                <RiThumbUpLine className="h-3.5 w-3.5 text-gray-700 dark:text-gray-300" />
                <span className="mt-1 text-sm leading-none font-semibold text-gray-700 dark:text-gray-300">
                  {upvoteCount}
                </span>
              </div>
            </TooltipTrigger>
            <TooltipContent side="top" className="z-100 text-xs">
              Upvoting closed
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  )
}
