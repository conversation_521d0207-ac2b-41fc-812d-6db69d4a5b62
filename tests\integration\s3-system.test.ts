/**
 * S3 System Integration Tests
 * 
 * Tests for the complete S3 upload system integration
 */

import { describe, it, expect } from 'vitest'
import { validateImageFile, generateFileKey } from '@/lib/file-validation'
import { generateS3Key } from '@/lib/s3-upload'

describe('S3 System Integration', () => {
  describe('File Processing Pipeline', () => {
    it('should process valid file through complete pipeline', () => {
      // Create test file
      const buffer = new Uint8Array([0xFF, 0xD8, 0xFF, 0xE0, ...new Array(1000).fill(0)])
      const file = new File([buffer], 'test.jpg', { type: 'image/jpeg' })
      
      // Step 1: Validate file
      const validationError = validateImageFile(file, 'logo')
      expect(validationError).toBeNull()
      
      // Step 2: Generate file key
      const fileKey = generateFileKey('user123', 'logo', file)
      expect(fileKey).toMatch(/^projects\/user123\/logo\/\d+-[a-z0-9]+\.jpg$/)
      
      // Step 3: Generate S3 key
      const s3Key = generateS3Key('user123', 'logo', 'jpg')
      expect(s3Key).toMatch(/^projects\/user123\/logo\/\d+-[a-z0-9]+\.jpg$/)
    })

    it('should reject invalid files in pipeline', () => {
      // Create invalid file (too large)
      const largeBuffer = new Uint8Array(2 * 1024 * 1024) // 2MB
      const file = new File([largeBuffer], 'large.jpg', { type: 'image/jpeg' })
      
      // Should fail validation
      const validationError = validateImageFile(file, 'logo')
      expect(validationError).not.toBeNull()
      expect(validationError?.code).toBe('FILE_TOO_LARGE')
    })
  })

  describe('Configuration Integration', () => {
    it('should have all required environment variables', () => {
      const requiredVars = [
        'AWS_S3_REGION',
        'AWS_S3_BUCKET_NAME',
        'AWS_S3_ACCESS_KEY_ID',
        'AWS_S3_SECRET_ACCESS_KEY',
        'AWS_S3_PUBLIC_URL'
      ]

      requiredVars.forEach(varName => {
        expect(process.env[varName]).toBeDefined()
        expect(process.env[varName]).not.toBe('')
      })
    })

    it('should have valid S3 configuration format', () => {
      expect(process.env.AWS_S3_REGION).toMatch(/^[a-z0-9-]+$/)
      expect(process.env.AWS_S3_BUCKET_NAME).toMatch(/^[a-z0-9.-]+$/)
      expect(process.env.AWS_S3_PUBLIC_URL).toMatch(/^https:\/\/.*\.s3\..*\.amazonaws\.com$/)
    })
  })

  describe('System Constants', () => {
    it('should have consistent file size limits', () => {
      const { FILE_SIZE_LIMITS } = require('@/lib/file-validation')
      
      expect(FILE_SIZE_LIMITS.LOGO).toBe(1024 * 1024) // 1MB
      expect(FILE_SIZE_LIMITS.PRODUCT_IMAGE).toBe(1024 * 1024) // 1MB
      expect(FILE_SIZE_LIMITS.MAX_FILE_SIZE).toBe(1024 * 1024) // 1MB
    })

    it('should have valid image types', () => {
      const { ALLOWED_IMAGE_TYPES } = require('@/lib/file-validation')
      
      expect(ALLOWED_IMAGE_TYPES).toContain('image/jpeg')
      expect(ALLOWED_IMAGE_TYPES).toContain('image/png')
      expect(ALLOWED_IMAGE_TYPES).toContain('image/webp')
      expect(ALLOWED_IMAGE_TYPES).toContain('image/gif')
    })
  })

  describe('Error Handling Integration', () => {
    it('should handle validation errors gracefully', () => {
      const invalidFile = new File([], 'empty.jpg', { type: 'image/jpeg' })
      
      const error = validateImageFile(invalidFile, 'logo')
      expect(error).not.toBeNull()
      expect(error?.code).toBe('FILE_CORRUPTED')
      expect(error?.message).toContain('corrupted')
    })

    it('should handle key generation errors', () => {
      expect(() => generateS3Key('', 'logo')).toThrow('User ID is required')
    })
  })

  describe('Compatibility Layer', () => {
    it('should support UploadThing endpoint mapping', () => {
      // Test endpoint mapping logic
      const logoEndpoints = ['logo', 'projectLogo']
      const productEndpoints = ['product-image', 'projectProductImage']
      
      logoEndpoints.forEach(endpoint => {
        const key = generateFileKey('user123', 'logo')
        expect(key).toContain('/logo/')
      })
      
      productEndpoints.forEach(endpoint => {
        const key = generateFileKey('user123', 'product-image')
        expect(key).toContain('/product-image/')
      })
    })
  })
})
