"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"

import {
  RiMoreLine,
  RiEditLine,
  RiDeleteBinLine,
  RiEyeLine,
  RiStarLine,
  RiStarFill,
  RiExternalLinkLine,
} from "@remixicon/react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { AdminProjectsPagination } from "./admin-projects-pagination"
import { AdminBulkActions } from "./admin-bulk-actions"
import { deleteAdminProject, toggleProjectFeatured } from "@/app/actions/admin"
import { toast } from "sonner"

interface Project {
  id: string
  name: string
  slug: string
  description: string
  websiteUrl: string
  logoUrl: string
  launchStatus: string
  launchType: string | null
  featuredOnHomepage: boolean | null
  dailyRanking: number | null
  createdAt: Date
  categories: { id: string; name: string }[]
}

interface AdminProjectsTableProps {
  projects: Project[]
  totalCount: number
  totalPages: number
  currentPage: number
}

const statusLabels = {
  scheduled: "Đã lên lịch",
  launched: "Đã ra mắt",
  draft: "Bản nháp",
  cancelled: "Đã hủy",
}

const statusVariants = {
  scheduled: "default",
  launched: "default",
  draft: "secondary",
  cancelled: "destructive",
} as const

const launchTypeLabels = {
  free: "Miễn phí",
  premium: "Premium",
  premium_plus: "Premium Plus",
}

const launchTypeVariants = {
  free: "secondary",
  premium: "default",
  premium_plus: "default",
} as const

export function AdminProjectsTable({
  projects,
  totalCount,
  totalPages,
  currentPage,
}: AdminProjectsTableProps) {
  const router = useRouter()
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProjects(projects.map((p) => p.id))
    } else {
      setSelectedProjects([])
    }
  }

  const handleSelectProject = (projectId: string, checked: boolean) => {
    if (checked) {
      setSelectedProjects([...selectedProjects, projectId])
    } else {
      setSelectedProjects(selectedProjects.filter((id) => id !== projectId))
    }
  }

  const handleDeleteProject = async (projectId: string) => {
    setIsDeleting(true)
    try {
      const result = await deleteAdminProject(projectId)
      if (result.success) {
        toast.success("Đã xóa project thành công")
        router.refresh()
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi xóa project")
      }
    } catch {
      toast.error("Có lỗi xảy ra khi xóa project")
    } finally {
      setIsDeleting(false)
      setDeleteDialogOpen(false)
      setProjectToDelete(null)
    }
  }

  const handleToggleFeatured = async (projectId: string) => {
    try {
      const result = await toggleProjectFeatured(projectId)
      if (result.success) {
        toast.success("Đã cập nhật trạng thái featured")
        router.refresh()
      } else {
        toast.error(result.error || "Có lỗi xảy ra")
      }
    } catch {
      toast.error("Có lỗi xảy ra")
    }
  }

  const isAllSelected = projects.length > 0 && selectedProjects.length === projects.length

  return (
    <div className="space-y-4">
      {/* Bulk actions */}
      {selectedProjects.length > 0 && (
        <AdminBulkActions
          selectedProjects={selectedProjects}
          onClearSelection={() => setSelectedProjects([])}
        />
      )}

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                  aria-label="Chọn tất cả projects"
                />
              </TableHead>
              <TableHead>Project</TableHead>
              <TableHead>Trạng thái</TableHead>
              <TableHead>Loại Launch</TableHead>
              <TableHead>Featured</TableHead>
              <TableHead>Xếp hạng</TableHead>
              <TableHead>Ngày tạo</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {projects.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                  Không tìm thấy projects nào
                </TableCell>
              </TableRow>
            ) : (
              projects.map((project) => (
                <TableRow key={project.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedProjects.includes(project.id)}
                      onCheckedChange={(checked) =>
                        handleSelectProject(project.id, !!checked)
                      }
                      aria-label={`Chọn ${project.name}`}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="relative h-10 w-10 overflow-hidden rounded-md border">
                        <Image
                          src={project.logoUrl}
                          alt={`${project.name} logo`}
                          fill
                          className="object-contain"
                          sizes="40px"
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="font-medium truncate">{project.name}</div>
                        <div className="text-sm text-muted-foreground truncate">
                          {project.description}
                        </div>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {project.categories.slice(0, 2).map((category) => (
                            <Badge key={category.id} variant="outline" className="text-xs">
                              {category.name}
                            </Badge>
                          ))}
                          {project.categories.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{project.categories.length - 2}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={statusVariants[project.launchStatus as keyof typeof statusVariants]}
                    >
                      {statusLabels[project.launchStatus as keyof typeof statusLabels]}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {project.launchType && (
                      <Badge
                        variant={launchTypeVariants[project.launchType as keyof typeof launchTypeVariants]}
                      >
                        {launchTypeLabels[project.launchType as keyof typeof launchTypeLabels]}
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleFeatured(project.id)}
                      className="h-8 w-8 p-0"
                    >
                      {project.featuredOnHomepage ? (
                        <RiStarFill className="h-4 w-4 text-yellow-500" />
                      ) : (
                        <RiStarLine className="h-4 w-4" />
                      )}
                    </Button>
                  </TableCell>
                  <TableCell>
                    {project.dailyRanking ? (
                      <Badge variant="outline">#{project.dailyRanking}</Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {new Date(project.createdAt).toLocaleDateString("vi-VN")}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <RiMoreLine className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/projects/${project.id}`}>
                            <RiEyeLine className="mr-2 h-4 w-4" />
                            Xem chi tiết
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/projects/${project.id}/edit`}>
                            <RiEditLine className="mr-2 h-4 w-4" />
                            Chỉnh sửa
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={project.websiteUrl} target="_blank">
                            <RiExternalLinkLine className="mr-2 h-4 w-4" />
                            Mở website
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => {
                            setProjectToDelete(project.id)
                            setDeleteDialogOpen(true)
                          }}
                          className="text-destructive focus:text-destructive"
                        >
                          <RiDeleteBinLine className="mr-2 h-4 w-4" />
                          Xóa project
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <AdminProjectsPagination
        totalPages={totalPages}
        currentPage={currentPage}
        totalCount={totalCount}
      />
      
      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa project</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa project này? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => projectToDelete && handleDeleteProject(projectToDelete)}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Đang xóa..." : "Xóa"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
