import { Upload } from "@aws-sdk/lib-storage"
import { DeleteObjectCommand, HeadObjectCommand } from "@aws-sdk/client-s3"
import { s3Client, S3_CONFIG } from "./s3-client"

/**
 * Upload Result Interface
 */
export interface UploadResult {
  url: string
  key: string
  size: number
  contentType: string
}

/**
 * Upload Error Types
 */
export class S3UploadError extends Error {
  constructor(message: string, public cause?: Error) {
    super(message)
    this.name = 'S3UploadError'
  }
}

/**
 * Upload file to AWS S3
 * 
 * @param file - Buffer của file cần upload
 * @param key - S3 key (path) cho file
 * @param contentType - MIME type của file
 * @returns Promise<UploadResult>
 */
export async function uploadToS3(
  file: Buffer,
  key: string,
  contentType: string
): Promise<UploadResult> {
  try {
    // Validate inputs
    if (!file || file.length === 0) {
      throw new S3UploadError("File buffer is empty or invalid")
    }

    if (!key || key.trim() === '') {
      throw new S3UploadError("S3 key is required")
    }

    if (!contentType) {
      throw new S3UploadError("Content type is required")
    }

    // Optimize part size based on file size
    const optimizedPartSize = file.length < 1024 * 1024 * 10 // 10MB
      ? 1024 * 1024 * 5  // 5MB for smaller files
      : 1024 * 1024 * 10 // 10MB for larger files

    // Create upload instance with optimized settings
    const upload = new Upload({
      client: s3Client,
      params: {
        Bucket: S3_CONFIG.BUCKET_NAME,
        Key: key,
        Body: file,
        ContentType: contentType,
        StorageClass: "STANDARD",
        // Optimized cache control
        CacheControl: "public, max-age=31536000, immutable", // 1 year with immutable
        // Compression for text-based files
        ContentEncoding: contentType.includes('text') ? 'gzip' : undefined,
        // Set metadata
        Metadata: {
          uploadedAt: new Date().toISOString(),
          originalSize: file.length.toString(),
          uploadedFrom: 'open-launch-app',
        },
      },
      // Optimized upload configuration
      queueSize: 6, // Increased concurrent uploads
      partSize: optimizedPartSize,
      leavePartsOnError: false, // Clean up failed uploads
    })

    // Perform upload
    const result = await upload.done()
    
    if (!result.Key) {
      throw new S3UploadError("Upload failed: No key returned from S3")
    }

    // Generate public URL
    const url = `${S3_CONFIG.PUBLIC_URL}/${key}`
    
    return {
      url,
      key: result.Key,
      size: file.length,
      contentType,
    }

  } catch (error) {
    console.error("S3 Upload Error:", error)
    
    if (error instanceof S3UploadError) {
      throw error
    }
    
    throw new S3UploadError(
      `Failed to upload file to S3: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error instanceof Error ? error : undefined
    )
  }
}

/**
 * Delete file from AWS S3
 * 
 * @param key - S3 key của file cần xóa
 * @returns Promise<void>
 */
export async function deleteFromS3(key: string): Promise<void> {
  try {
    if (!key || key.trim() === '') {
      throw new S3UploadError("S3 key is required for deletion")
    }

    const command = new DeleteObjectCommand({
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: key,
    })

    await s3Client.send(command)
    
    console.log(`Successfully deleted file: ${key}`)

  } catch (error) {
    console.error("S3 Delete Error:", error)
    
    throw new S3UploadError(
      `Failed to delete file from S3: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error instanceof Error ? error : undefined
    )
  }
}

/**
 * Check if file exists in S3
 * 
 * @param key - S3 key của file cần kiểm tra
 * @returns Promise<boolean>
 */
export async function fileExistsInS3(key: string): Promise<boolean> {
  try {
    if (!key || key.trim() === '') {
      return false
    }

    const command = new HeadObjectCommand({
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: key,
    })

    await s3Client.send(command)
    return true

  } catch (error: unknown) {
    // If file doesn't exist, AWS returns 404
    if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
      return false
    }
    
    // For other errors, log and return false
    console.error("Error checking file existence:", error)
    return false
  }
}

/**
 * Generate unique S3 key for file upload
 * 
 * @param userId - ID của user
 * @param type - Loại file ('logo' | 'product-image')
 * @param extension - File extension (optional)
 * @returns string - Unique S3 key
 */
export function generateS3Key(
  userId: string, 
  type: 'logo' | 'product-image',
  extension?: string
): string {
  if (!userId || userId.trim() === '') {
    throw new S3UploadError("User ID is required for generating S3 key")
  }

  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 15)
  const ext = extension ? `.${extension.replace('.', '')}` : ''
  
  return `projects/${userId}/${type}/${timestamp}-${random}${ext}`
}

/**
 * Extract S3 key from URL
 *
 * @param url - Full S3 URL
 * @returns string | null - S3 key or null if invalid URL
 */
export function extractS3KeyFromUrl(url: string): string | null {
  try {
    if (!url || typeof url !== 'string') {
      return null
    }

    // Remove query parameters
    const cleanUrl = url.split('?')[0]

    // Extract key from S3 URL
    const s3BaseUrl = S3_CONFIG.PUBLIC_URL
    if (cleanUrl.startsWith(s3BaseUrl)) {
      return cleanUrl.replace(`${s3BaseUrl}/`, '')
    }

    return null
  } catch (error) {
    console.error("Error extracting S3 key from URL:", error)
    return null
  }
}

// Re-export everything from other modules for convenience
export * from './s3-upload-components'
export * from './file-validation'
export { s3Client, S3_CONFIG, validateS3Config } from './s3-client'
