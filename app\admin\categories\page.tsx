import { Tag } from "lucide-react"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

export default function AdminCategoriesPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Tag className="h-6 w-6" />
        <h1 className="text-2xl font-bold">Quản lý Categories</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Categories Management</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Trang quản lý danh mục đang được phát triển...
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
