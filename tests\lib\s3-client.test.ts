/**
 * S3 Client Unit Tests
 * 
 * Tests for lib/s3-client.ts
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock AWS SDK
vi.mock('@aws-sdk/client-s3', () => {
  const mockS3Client = vi.fn().mockImplementation((config) => ({
    config,
    send: vi.fn(),
  }))

  return {
    S3Client: mockS3Client,
  }
})

describe('S3 Client', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset environment variables
    process.env.AWS_S3_REGION = 'ap-southeast-1'
    process.env.AWS_S3_BUCKET_NAME = 'test-bucket'
    process.env.AWS_S3_ACCESS_KEY_ID = 'test-key'
    process.env.AWS_S3_SECRET_ACCESS_KEY = 'test-secret'
    process.env.AWS_S3_PUBLIC_URL = 'https://test-bucket.s3.ap-southeast-1.amazonaws.com'
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('validateS3Config', () => {
    it('should validate complete configuration', async () => {
      // Re-import to get fresh module with current env vars
      const { validateS3Config } = await import('@/lib/s3-client')
      
      expect(() => validateS3Config()).not.toThrow()
    })

    it('should throw error for missing region', async () => {
      delete process.env.AWS_S3_REGION
      
      // Clear module cache and re-import
      vi.resetModules()
      const { validateS3Config } = await import('@/lib/s3-client')
      
      expect(() => validateS3Config()).toThrow('Missing required S3 environment variables')
    })

    it('should throw error for missing bucket name', async () => {
      delete process.env.AWS_S3_BUCKET_NAME
      
      vi.resetModules()
      const { validateS3Config } = await import('@/lib/s3-client')
      
      expect(() => validateS3Config()).toThrow('Missing required S3 environment variables')
    })

    it('should throw error for missing access key', async () => {
      delete process.env.AWS_S3_ACCESS_KEY_ID
      
      vi.resetModules()
      const { validateS3Config } = await import('@/lib/s3-client')
      
      expect(() => validateS3Config()).toThrow('Missing required S3 environment variables')
    })

    it('should throw error for missing secret key', async () => {
      delete process.env.AWS_S3_SECRET_ACCESS_KEY
      
      vi.resetModules()
      const { validateS3Config } = await import('@/lib/s3-client')
      
      expect(() => validateS3Config()).toThrow('Missing required S3 environment variables')
    })

    it('should throw error for missing public URL', async () => {
      delete process.env.AWS_S3_PUBLIC_URL
      
      vi.resetModules()
      const { validateS3Config } = await import('@/lib/s3-client')
      
      expect(() => validateS3Config()).toThrow('Missing required S3 environment variables')
    })

    it('should throw error for empty values', async () => {
      process.env.AWS_S3_REGION = ''
      
      vi.resetModules()
      const { validateS3Config } = await import('@/lib/s3-client')
      
      expect(() => validateS3Config()).toThrow('Missing required S3 environment variables')
    })
  })

  describe('S3_CONFIG', () => {
    it('should have correct configuration values', async () => {
      const { S3_CONFIG } = await import('@/lib/s3-client')
      
      expect(S3_CONFIG.REGION).toBe('ap-southeast-1')
      expect(S3_CONFIG.BUCKET_NAME).toBe('test-bucket')
      expect(S3_CONFIG.ACCESS_KEY_ID).toBe('test-key')
      expect(S3_CONFIG.SECRET_ACCESS_KEY).toBe('test-secret')
      expect(S3_CONFIG.PUBLIC_URL).toBe('https://test-bucket.s3.ap-southeast-1.amazonaws.com')
    })
  })

  describe('s3Client', () => {
    it('should create S3Client with correct configuration', async () => {
      const { S3Client } = await import('@aws-sdk/client-s3')
      const { s3Client } = await import('@/lib/s3-client')
      
      expect(S3Client).toHaveBeenCalledWith({
        region: 'ap-southeast-1',
        credentials: {
          accessKeyId: 'test-key',
          secretAccessKey: 'test-secret',
        },
      })
      
      expect(s3Client).toBeDefined()
    })

    it('should have send method', async () => {
      const { s3Client } = await import('@/lib/s3-client')
      
      expect(s3Client.send).toBeDefined()
      expect(typeof s3Client.send).toBe('function')
    })
  })

  describe('Environment Variable Handling', () => {
    it('should handle different environment configurations', async () => {
      // Test with different region
      process.env.AWS_S3_REGION = 'us-east-1'
      process.env.AWS_S3_BUCKET_NAME = 'prod-bucket'
      process.env.AWS_S3_PUBLIC_URL = 'https://prod-bucket.s3.us-east-1.amazonaws.com'
      
      vi.resetModules()
      const { S3_CONFIG } = await import('@/lib/s3-client')
      
      expect(S3_CONFIG.REGION).toBe('us-east-1')
      expect(S3_CONFIG.BUCKET_NAME).toBe('prod-bucket')
      expect(S3_CONFIG.PUBLIC_URL).toBe('https://prod-bucket.s3.us-east-1.amazonaws.com')
    })

    it('should handle production environment', async () => {
      process.env.NODE_ENV = 'production'
      process.env.AWS_S3_BUCKET_NAME = 'open-launch-uploads'
      process.env.AWS_S3_PUBLIC_URL = 'https://open-launch-uploads.s3.ap-southeast-1.amazonaws.com'
      
      vi.resetModules()
      const { S3_CONFIG } = await import('@/lib/s3-client')
      
      expect(S3_CONFIG.BUCKET_NAME).toBe('open-launch-uploads')
      expect(S3_CONFIG.PUBLIC_URL).toBe('https://open-launch-uploads.s3.ap-southeast-1.amazonaws.com')
    })

    it('should handle development environment', async () => {
      process.env.NODE_ENV = 'development'
      process.env.AWS_S3_BUCKET_NAME = 'open-launch-uploads-dev'
      process.env.AWS_S3_PUBLIC_URL = 'https://open-launch-uploads-dev.s3.ap-southeast-1.amazonaws.com'
      
      vi.resetModules()
      const { S3_CONFIG } = await import('@/lib/s3-client')
      
      expect(S3_CONFIG.BUCKET_NAME).toBe('open-launch-uploads-dev')
      expect(S3_CONFIG.PUBLIC_URL).toBe('https://open-launch-uploads-dev.s3.ap-southeast-1.amazonaws.com')
    })
  })

  describe('Configuration Validation Edge Cases', () => {
    it('should handle whitespace-only values', async () => {
      process.env.AWS_S3_REGION = '   '
      
      vi.resetModules()
      const { validateS3Config } = await import('@/lib/s3-client')
      
      expect(() => validateS3Config()).toThrow('Missing required S3 environment variables')
    })

    it('should handle undefined vs empty string', async () => {
      process.env.AWS_S3_REGION = undefined as any
      
      vi.resetModules()
      const { validateS3Config } = await import('@/lib/s3-client')
      
      expect(() => validateS3Config()).toThrow('Missing required S3 environment variables')
    })

    it('should validate all required variables in single call', async () => {
      // Remove multiple variables
      delete process.env.AWS_S3_REGION
      delete process.env.AWS_S3_BUCKET_NAME
      delete process.env.AWS_S3_ACCESS_KEY_ID
      
      vi.resetModules()
      const { validateS3Config } = await import('@/lib/s3-client')
      
      expect(() => validateS3Config()).toThrow('Missing required S3 environment variables')
    })
  })

  describe('Module Initialization', () => {
    it('should initialize without throwing when all env vars are present', async () => {
      expect(() => {
        vi.resetModules()
        import('@/lib/s3-client')
      }).not.toThrow()
    })

    it('should create client instance on import', async () => {
      const { s3Client } = await import('@/lib/s3-client')
      
      expect(s3Client).toBeDefined()
      expect(typeof s3Client).toBe('object')
    })
  })
})
