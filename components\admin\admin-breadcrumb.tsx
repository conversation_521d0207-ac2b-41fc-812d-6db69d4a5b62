"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

import { RiHomeLine, RiArrowRightSLine } from "@remixicon/react"

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

interface BreadcrumbItem {
  label: string
  href?: string
}

export function AdminBreadcrumb() {
  const pathname = usePathname()
  
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const segments = pathname.split("/").filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = []

    // Always start with Admin
    breadcrumbs.push({ label: "Admin", href: "/admin" })

    if (segments.length > 1) {
      // Handle different admin sections
      switch (segments[1]) {
        case "projects":
          breadcrumbs.push({ label: "Projects", href: "/admin/projects" })
          
          if (segments[2]) {
            // Project detail or edit
            if (segments[3] === "edit") {
              breadcrumbs.push({ label: "Chi tiết", href: `/admin/projects/${segments[2]}` })
              breadcrumbs.push({ label: "Chỉnh sửa" })
            } else {
              breadcrumbs.push({ label: "Chi tiết" })
            }
          }
          break
          
        case "users":
          breadcrumbs.push({ label: "Users", href: "/admin/users" })
          if (segments[2]) {
            breadcrumbs.push({ label: "Chi tiết" })
          }
          break
          
        case "categories":
          breadcrumbs.push({ label: "Categories", href: "/admin/categories" })
          if (segments[2]) {
            breadcrumbs.push({ label: "Chi tiết" })
          }
          break
          
        case "analytics":
          breadcrumbs.push({ label: "Analytics" })
          break
          
        case "settings":
          breadcrumbs.push({ label: "Settings" })
          break
          
        default:
          // Fallback for unknown routes
          breadcrumbs.push({ label: segments[1].charAt(0).toUpperCase() + segments[1].slice(1) })
      }
    }

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  if (breadcrumbs.length <= 1) {
    return null
  }

  return (
    <Breadcrumb className="mb-6">
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link href="/" className="flex items-center gap-1">
              <RiHomeLine className="h-4 w-4" />
              Trang chủ
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        
        {breadcrumbs.map((item, index) => (
          <div key={index} className="flex items-center">
            <BreadcrumbSeparator>
              <RiArrowRightSLine className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              {item.href && index < breadcrumbs.length - 1 ? (
                <BreadcrumbLink asChild>
                  <Link href={item.href}>{item.label}</Link>
                </BreadcrumbLink>
              ) : (
                <BreadcrumbPage>{item.label}</BreadcrumbPage>
              )}
            </BreadcrumbItem>
          </div>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
