# 🧪 S3 Upload System Tests

## 📋 Test Overview

Comprehensive unit and integration tests for the S3 upload system migration from UploadThing to AWS S3.

## 🏗️ Test Structure

```
tests/
├── setup.ts                           # Test configuration and global mocks
├── lib/
│   ├── file-validation.test.ts        # File validation utilities tests
│   ├── s3-upload.test.ts              # S3 upload functions tests
│   ├── s3-client.test.ts              # S3 client configuration tests
│   └── s3-upload-components.test.tsx  # React components tests
├── api/
│   └── upload.test.ts                 # API routes integration tests
└── README.md                          # This file
```

## 🚀 Running Tests

### **Basic Commands**
```bash
# Run all tests
bun test

# Run tests in watch mode
bun run test:watch

# Run tests once
bun run test:run

# Run with UI
bun run test:ui

# Generate coverage report
bun run test:coverage
```

### **Specific Test Files**
```bash
# Run specific test file
bun test tests/lib/file-validation.test.ts

# Run tests matching pattern
bun test --grep "validation"

# Run tests for specific component
bun test s3-upload-components
```

## 📊 Test Coverage

### **Target Coverage**
- **Functions**: 95%+
- **Lines**: 90%+
- **Branches**: 85%+
- **Statements**: 90%+

### **Coverage Areas**

#### **✅ File Validation (`lib/file-validation.ts`)**
- ✅ File size validation
- ✅ MIME type validation  
- ✅ File name validation
- ✅ Extension detection
- ✅ Key generation
- ✅ Multiple file validation
- ✅ Error handling
- ✅ Edge cases

#### **✅ S3 Upload Functions (`lib/s3-upload.ts`)**
- ✅ File upload to S3
- ✅ File deletion from S3
- ✅ File existence checking
- ✅ S3 key generation
- ✅ URL key extraction
- ✅ Error handling
- ✅ Buffer validation

#### **✅ S3 Client (`lib/s3-client.ts`)**
- ✅ Configuration validation
- ✅ Environment variable handling
- ✅ Client initialization
- ✅ Error scenarios
- ✅ Different environments

#### **✅ React Components (`lib/s3-upload-components.tsx`)**
- ✅ Component rendering
- ✅ File upload flow
- ✅ Error handling
- ✅ Loading states
- ✅ Props validation
- ✅ Event callbacks
- ✅ Endpoint mapping

#### **✅ API Routes (`app/api/upload/*`)**
- ✅ Authentication
- ✅ File upload processing
- ✅ Validation integration
- ✅ S3 integration
- ✅ Error responses
- ✅ CORS handling

## 🔧 Test Configuration

### **Vitest Configuration (`vitest.config.ts`)**
```typescript
export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*',
        'scripts/',
        'drizzle/',
        '.next/',
        'docs/',
      ],
    },
    testTimeout: 30000,
  },
})
```

### **Test Setup (`tests/setup.ts`)**
- Global mocks for console, fetch, File API
- Environment variable setup
- Testing Library configuration
- AWS SDK mocking

## 🎯 Test Scenarios

### **Unit Tests**

#### **File Validation**
- ✅ Valid file acceptance
- ✅ Invalid file rejection
- ✅ Size limit enforcement
- ✅ Type checking
- ✅ Name validation
- ✅ Extension handling

#### **S3 Operations**
- ✅ Successful uploads
- ✅ Upload failures
- ✅ File deletion
- ✅ Existence checking
- ✅ Key generation
- ✅ URL parsing

#### **React Components**
- ✅ Rendering with different props
- ✅ User interactions
- ✅ File selection
- ✅ Upload progress
- ✅ Error states
- ✅ Loading states

### **Integration Tests**

#### **API Routes**
- ✅ End-to-end upload flow
- ✅ Authentication integration
- ✅ Validation integration
- ✅ S3 integration
- ✅ Error handling
- ✅ Response formats

## 🐛 Mocking Strategy

### **AWS SDK Mocking**
```typescript
vi.mock('@aws-sdk/lib-storage', () => ({
  Upload: vi.fn().mockImplementation(() => ({
    done: vi.fn().mockResolvedValue({
      Key: 'test-key',
      Location: 'https://test-bucket.s3.amazonaws.com/test-key',
    }),
  })),
}))
```

### **Authentication Mocking**
```typescript
vi.mock('@/lib/auth', () => ({
  auth: {
    api: {
      getSession: vi.fn().mockResolvedValue({
        user: { id: 'user123' }
      })
    }
  }
}))
```

### **File API Mocking**
```typescript
global.File = class File {
  constructor(bits: BlobPart[], name: string, options?: FilePropertyBag) {
    // Mock implementation
  }
}
```

## 📈 Test Metrics

### **Performance Benchmarks**
- File validation: <5ms
- S3 key generation: <1ms
- Component rendering: <50ms
- API response time: <100ms (mocked)

### **Test Execution Time**
- Unit tests: ~2-5 seconds
- Integration tests: ~5-10 seconds
- Full test suite: ~10-15 seconds

## 🔍 Debugging Tests

### **Common Issues**

#### **1. Mock Not Working**
```bash
# Clear module cache
vi.resetModules()

# Re-import after mocking
const { function } = await import('@/lib/module')
```

#### **2. Async Test Failures**
```typescript
// Use waitFor for async operations
await waitFor(() => {
  expect(mockFunction).toHaveBeenCalled()
})
```

#### **3. File Upload Testing**
```typescript
// Use userEvent for file uploads
const user = userEvent.setup()
await user.upload(fileInput, file)
```

### **Debug Commands**
```bash
# Run tests with verbose output
bun test --reporter=verbose

# Run single test with debugging
bun test --grep "specific test" --reporter=verbose

# Generate detailed coverage
bun run test:coverage --reporter=html
```

## 🎯 Best Practices

### **Test Writing**
1. **Arrange-Act-Assert** pattern
2. **Descriptive test names**
3. **Isolated test cases**
4. **Proper mocking**
5. **Edge case coverage**

### **Mock Management**
1. **Clear mocks between tests**
2. **Reset modules when needed**
3. **Mock at appropriate level**
4. **Verify mock calls**

### **Async Testing**
1. **Use waitFor for async operations**
2. **Proper timeout handling**
3. **Promise resolution testing**
4. **Error scenario testing**

## 📚 Resources

- [Vitest Documentation](https://vitest.dev/)
- [Testing Library](https://testing-library.com/)
- [Jest DOM Matchers](https://github.com/testing-library/jest-dom)
- [User Event](https://testing-library.com/docs/user-event/intro/)

---

**Test Suite Status: ✅ Complete**

*All core functionality covered with comprehensive unit and integration tests.*
