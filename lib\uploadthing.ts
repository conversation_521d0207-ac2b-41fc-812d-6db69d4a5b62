/**
 * UploadThing Compatibility Layer
 *
 * Thay thế hoàn toàn lib/uploadthing.ts cũ
 * Export S3 components với tên tương thích UploadThing
 */

import { S3UploadButton, S3UploadDropzone } from './s3-upload-components'
import type { S3UploadButtonProps, S3UploadResponse } from './s3-upload-components'

// Export với tên tương thích UploadThing
export const UploadButton = S3UploadButton
export const UploadDropzone = S3UploadDropzone

// Export types với tên tương thích
export type UploadButtonProps = S3UploadButtonProps
export type UploadResponse = S3UploadResponse

/**
 * Mock OurFileRouter type để tương thích với existing code
 * Không cần thiết nữa vì chúng ta không sử dụng UploadThing
 */
export type OurFileRouter = {
  projectLogo: unknown
  projectProductImage: unknown
}
