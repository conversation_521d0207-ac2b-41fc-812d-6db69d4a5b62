---
description:
globs:
alwaysApply: false
---
# Server Actions

Open-Launch sử dụng Server Actions của Next.js để xử lý các thao tác từ client. Các server actions được định nghĩa trong thư mục [app/actions](mdc:app/actions/).

## Cấu trúc Server Actions

- [app/actions/projects.ts](mdc:app/actions/projects.ts): Actions liên quan đến dự án
- [app/actions/launch.ts](mdc:app/actions/launch.ts): Actions liên quan đến ra mắt sản phẩm
- [app/actions/admin.ts](mdc:app/actions/admin.ts): Actions dành cho quản trị viên
- [app/actions/home.ts](mdc:app/actions/home.ts): Actions cho trang chủ
- [app/actions/winners.ts](mdc:app/actions/winners.ts): Actions liên quan đến người chiến thắng
- [app/actions/project-details.ts](mdc:app/actions/project-details.ts): Actions cho chi tiết dự án
- [app/actions/discord.ts](mdc:app/actions/discord.ts): Actions liên quan đến Discord
- [app/actions/umami.ts](mdc:app/actions/umami.ts): Actions liên quan đến phân tích Umami

## Server Actions chính

### Projects

- `createProject`: Tạo dự án mới
- `updateProject`: Cập nhật dự án
- `deleteProject`: Xóa dự án
- `getProjects`: Lấy danh sách dự án
- `getProjectBySlug`: Lấy dự án theo slug

### Launch

- `getLaunchByDate`: Lấy thông tin ra mắt theo ngày
- `getCurrentLaunch`: Lấy thông tin ra mắt hiện tại
- `getUpcomingLaunches`: Lấy danh sách ra mắt sắp tới

### Admin

- `getAdminProjects`: Lấy danh sách dự án cho quản trị viên
- `approveProject`: Phê duyệt dự án
- `rejectProject`: Từ chối dự án

## Sử dụng Server Actions

Các server actions được sử dụng trong các components thông qua `use server` directive hoặc được import trực tiếp từ các file actions.
