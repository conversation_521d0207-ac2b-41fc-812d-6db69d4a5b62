/**
 * Upload API Integration Tests
 * 
 * Tests for app/api/upload/* routes
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

describe('Upload API Routes', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('POST /api/upload/logo', () => {
    it('should upload logo successfully', async () => {
      // Mock authenticated user
      const { auth } = await import('@/lib/auth')
      ;(auth.api.getSession as any).mockResolvedValue({
        user: { id: 'user123' }
      })

      // Mock successful validation
      const { validateImageFile } = await import('@/lib/file-validation')
      ;(validateImageFile as any).mockReturnValue(null)

      // Mock successful S3 upload
      const { uploadToS3 } = await import('@/lib/s3-upload')
      ;(uploadToS3 as any).mockResolvedValue({
        url: 'https://test-bucket.s3.amazonaws.com/test-logo.jpg',
        key: 'projects/user123/logo/test-logo.jpg',
        size: 1024,
        contentType: 'image/jpeg'
      })

      // Mock file key generation
      const { generateFileKey } = await import('@/lib/file-validation')
      ;(generateFileKey as any).mockReturnValue('projects/user123/logo/test-logo.jpg')

      // Import route handler
      const { POST } = await import('@/app/api/upload/logo/route')

      // Create test request with FormData
      const formData = new FormData()
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      formData.append('file', file)

      const request = new NextRequest('http://localhost:3000/api/upload/logo', {
        method: 'POST',
        body: formData
      })

      // Execute request
      const response = await POST(request)
      const result = await response.json()

      // Assertions
      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.data).toEqual({
        url: 'https://test-bucket.s3.amazonaws.com/test-logo.jpg',
        key: 'projects/user123/logo/test-logo.jpg',
        size: 1024,
        contentType: 'image/jpeg',
        uploadedBy: 'user123',
        fileUrl: 'https://test-bucket.s3.amazonaws.com/test-logo.jpg'
      })
    })

    it('should return 401 for unauthenticated user', async () => {
      // Mock unauthenticated user
      const { auth } = await import('@/lib/auth')
      ;(auth.api.getSession as any).mockResolvedValue(null)

      const { POST } = await import('@/app/api/upload/logo/route')

      const formData = new FormData()
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      formData.append('file', file)

      const request = new NextRequest('http://localhost:3000/api/upload/logo', {
        method: 'POST',
        body: formData
      })

      const response = await POST(request)
      const result = await response.json()

      expect(response.status).toBe(401)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Unauthorized')
    })

    it('should return 400 for missing file', async () => {
      // Mock authenticated user
      const { auth } = await import('@/lib/auth')
      ;(auth.api.getSession as any).mockResolvedValue({
        user: { id: 'user123' }
      })

      const { POST } = await import('@/app/api/upload/logo/route')

      const formData = new FormData()
      // No file added

      const request = new NextRequest('http://localhost:3000/api/upload/logo', {
        method: 'POST',
        body: formData
      })

      const response = await POST(request)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toBe('No file provided')
    })

    it('should return 400 for validation error', async () => {
      // Mock authenticated user
      const { auth } = await import('@/lib/auth')
      ;(auth.api.getSession as any).mockResolvedValue({
        user: { id: 'user123' }
      })

      // Mock validation error
      const { validateImageFile } = await import('@/lib/file-validation')
      ;(validateImageFile as any).mockReturnValue({
        code: 'FILE_TOO_LARGE',
        message: 'File is too large',
        details: 'Max size is 1MB'
      })

      const { POST } = await import('@/app/api/upload/logo/route')

      const formData = new FormData()
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      formData.append('file', file)

      const request = new NextRequest('http://localhost:3000/api/upload/logo', {
        method: 'POST',
        body: formData
      })

      const response = await POST(request)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toBe('FILE_TOO_LARGE')
      expect(result.message).toBe('File is too large')
    })

    it('should return 500 for S3 upload error', async () => {
      // Mock authenticated user
      const { auth } = await import('@/lib/auth')
      ;(auth.api.getSession as any).mockResolvedValue({
        user: { id: 'user123' }
      })

      // Mock successful validation
      const { validateImageFile } = await import('@/lib/file-validation')
      ;(validateImageFile as any).mockReturnValue(null)

      // Mock S3 upload error
      const { uploadToS3, S3UploadError } = await import('@/lib/s3-upload')
      ;(uploadToS3 as any).mockRejectedValue(new S3UploadError('S3 upload failed'))

      const { POST } = await import('@/app/api/upload/logo/route')

      const formData = new FormData()
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      formData.append('file', file)

      const request = new NextRequest('http://localhost:3000/api/upload/logo', {
        method: 'POST',
        body: formData
      })

      const response = await POST(request)
      const result = await response.json()

      expect(response.status).toBe(500)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Upload failed')
    })
  })

  describe('POST /api/upload/product-image', () => {
    it('should upload product image successfully', async () => {
      // Mock authenticated user
      const { auth } = await import('@/lib/auth')
      ;(auth.api.getSession as any).mockResolvedValue({
        user: { id: 'user123' }
      })

      // Mock successful validation
      const { validateImageFile } = await import('@/lib/file-validation')
      ;(validateImageFile as any).mockReturnValue(null)

      // Mock successful S3 upload
      const { uploadToS3 } = await import('@/lib/s3-upload')
      ;(uploadToS3 as any).mockResolvedValue({
        url: 'https://test-bucket.s3.amazonaws.com/test-product.jpg',
        key: 'projects/user123/product-image/test-product.jpg',
        size: 2048,
        contentType: 'image/jpeg'
      })

      // Mock file key generation
      const { generateFileKey } = await import('@/lib/file-validation')
      ;(generateFileKey as any).mockReturnValue('projects/user123/product-image/test-product.jpg')

      // Import route handler
      const { POST } = await import('@/app/api/upload/product-image/route')

      // Create test request
      const formData = new FormData()
      const file = new File(['test content'], 'product.jpg', { type: 'image/jpeg' })
      formData.append('file', file)

      const request = new NextRequest('http://localhost:3000/api/upload/product-image', {
        method: 'POST',
        body: formData
      })

      // Execute request
      const response = await POST(request)
      const result = await response.json()

      // Assertions
      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.data.url).toBe('https://test-bucket.s3.amazonaws.com/test-product.jpg')
      expect(result.data.uploadedBy).toBe('user123')
    })

    it('should handle GET request for endpoint info', async () => {
      const { GET } = await import('@/app/api/upload/product-image/route')

      const request = new NextRequest('http://localhost:3000/api/upload/product-image', {
        method: 'GET'
      })

      const response = await GET()
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.message).toBe('Product Image Upload API is ready')
      expect(result.endpoint).toBe('/api/upload/product-image')
    })
  })

  describe('OPTIONS requests', () => {
    it('should handle CORS preflight for logo upload', async () => {
      const { OPTIONS } = await import('@/app/api/upload/logo/route')

      const response = await OPTIONS()

      expect(response.status).toBe(200)
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*')
      expect(response.headers.get('Access-Control-Allow-Methods')).toBe('POST, OPTIONS')
    })

    it('should handle CORS preflight for product image upload', async () => {
      const { OPTIONS } = await import('@/app/api/upload/product-image/route')

      const response = await OPTIONS()

      expect(response.status).toBe(200)
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*')
      expect(response.headers.get('Access-Control-Allow-Methods')).toBe('POST, OPTIONS')
    })
  })
})
