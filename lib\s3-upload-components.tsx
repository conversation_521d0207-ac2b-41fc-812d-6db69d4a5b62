"use client"

import { useState, useRef } from "react"
import { RiImageAddLine, RiLoader4Line } from "@remixicon/react"

/**
 * S3 Upload Response Interface
 * Tương thích với UploadThing response format
 */
export interface S3UploadResponse {
  success: boolean
  data?: {
    url: string
    key: string
    size: number
    contentType: string
    uploadedBy: string
    fileUrl: string // Tương thích với UploadThing
  }
  error?: string
  message?: string
  details?: string
}

/**
 * S3 Upload Button Props
 * Tương thích với UploadThing UploadButton props
 */
export interface S3UploadButtonProps {
  endpoint: 'logo' | 'product-image' | 'projectLogo' | 'projectProductImage'
  onUploadBegin?: () => void
  onClientUploadComplete?: (res: S3UploadResponse[]) => void
  onUploadError?: (error: Error) => void
  disabled?: boolean
  className?: string
  appearance?: {
    button?: string
    allowedContent?: string
  }
  content?: {
    button?: (props: { ready: boolean; isUploading: boolean }) => React.ReactNode
  }
}

/**
 * S3UploadButton Component
 * 
 * Thay thế hoàn toàn UploadButton của UploadThing
 * Tương thích với API và props của UploadThing
 */
export function S3UploadButton({
  endpoint,
  onUploadBegin,
  onClientUploadComplete,
  onUploadError,
  disabled = false,
  className = "",
  appearance,
  content
}: S3UploadButtonProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [ready, setReady] = useState(true)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Map endpoint to API URL (support both new and UploadThing format)
  const getApiUrl = (endpoint: string) => {
    switch (endpoint) {
      case 'logo':
      case 'projectLogo':
        return '/api/upload/logo'
      case 'product-image':
      case 'projectProductImage':
        return '/api/upload/product-image'
      default:
        throw new Error(`Unknown endpoint: ${endpoint}`)
    }
  }

  const handleClick = () => {
    if (disabled || isUploading || !ready) return
    fileInputRef.current?.click()
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploading(true)
    setReady(false)
    onUploadBegin?.()

    try {
      const formData = new FormData()
      formData.append('file', file)

      const apiUrl = getApiUrl(endpoint)
      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData,
      })

      const result: S3UploadResponse = await response.json()

      if (result.success && result.data) {
        // Format response tương thích với UploadThing
        // UploadThing trả về array với serverData property
        const uploadThingCompatibleResponse = [{
          serverData: {
            fileUrl: result.data.fileUrl,
            uploadedBy: result.data.uploadedBy
          },
          url: result.data.url,
          key: result.data.key,
          size: result.data.size,
          type: result.data.contentType
        }] as unknown[]

        onClientUploadComplete?.(uploadThingCompatibleResponse)
      } else {
        throw new Error(result.message || result.error || 'Upload failed')
      }
    } catch (error) {
      console.error('S3 Upload error:', error)
      const uploadError = error instanceof Error ? error : new Error('Upload failed')
      onUploadError?.(uploadError)
    } finally {
      setIsUploading(false)
      setReady(true)
      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  // Default button content
  const defaultButtonContent = ({ ready, isUploading }: { ready: boolean; isUploading: boolean }) => {
    if (isUploading) return <RiLoader4Line className="h-4 w-4 animate-spin" />
    if (ready) {
      const isLogo = endpoint === 'logo' || endpoint === 'projectLogo'
      return (
        <>
          <RiImageAddLine className="h-4 w-4" />
          {isLogo ? 'Upload Logo' : 'Add Product Image'}
        </>
      )
    }
    return "Getting ready..."
  }

  // Use custom content or default
  const buttonContent = content?.button 
    ? content.button({ ready, isUploading })
    : defaultButtonContent({ ready, isUploading })

  // Default button styling (tương thích với UploadThing)
  const defaultButtonClass = `
    ut-button border border-input bg-background hover:bg-accent hover:text-accent-foreground 
    text-sm h-9 px-3 inline-flex items-center justify-center gap-2 rounded-md
    transition-colors duration-200
    ${(disabled || isUploading || !ready) ? "opacity-50 pointer-events-none" : ""}
    ${className}
  `

  // Use custom appearance or default
  const buttonClass = appearance?.button || defaultButtonClass

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        style={{ display: 'none' }}
        disabled={disabled || isUploading || !ready}
      />
      <button
        type="button"
        onClick={handleClick}
        disabled={disabled || isUploading || !ready}
        className={buttonClass}
      >
        {buttonContent}
      </button>
    </>
  )
}

/**
 * S3UploadDropzone Component (placeholder for future implementation)
 * 
 * Hiện tại chưa cần thiết vì dự án chỉ sử dụng UploadButton
 * Có thể implement sau nếu cần
 */
export function S3UploadDropzone() {
  return (
    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
      <p className="text-gray-500">S3UploadDropzone - Coming Soon</p>
      <p className="text-sm text-gray-400">Use S3UploadButton for now</p>
    </div>
  )
}
