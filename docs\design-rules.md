# Quy Tắc <PERSON>hiế<PERSON>ế UI/UX - Open Launch

## 📋 Tổng Quan

Tài liệu này định nghĩa các quy tắc thiết kế UI/UX chặt chẽ cho dự án Open Launch, đ<PERSON><PERSON> b<PERSON><PERSON><PERSON> nh<PERSON><PERSON> qu<PERSON>, <PERSON><PERSON><PERSON> năng bảo trì và trải nghiệm người dùng tối ưu.

## 🎨 Design System

### Color Tokens

#### Primary Colors
```css
--primary: rgb(37, 99, 235)           /* Blue-600 */
--primary-foreground: hsl(0 0% 100%)  /* White */
```

#### Semantic Colors
```css
--background: hsl(0 0% 100%)          /* Light mode */
--foreground: hsl(0 0% 3.9%)          /* Dark gray */
--card: hsl(0 0% 100%)                /* White */
--card-foreground: hsl(0 0% 3.9%)     /* Dark gray */
--muted: hsl(0 0% 96.1%)              /* Light gray */
--muted-foreground: hsl(0 0% 45.1%)   /* Medium gray */
--accent: hsl(217, 91%, 60%)          /* Blue accent */
--destructive: hsl(0 84.2% 60.2%)     /* Red for errors */
--border: hsl(0 0% 89.8%)             /* Light border */
--input: hsl(0 0% 89.8%)              /* Input border */
--ring: hsl(217, 91%, 60%)            /* Focus ring */
```

#### Dark Mode Colors
```css
--background: hsl(0 0% 10%)           /* Dark background */
--foreground: hsl(0 0% 98%)           /* Light text */
--card: hsl(0 0% 12%)                 /* Dark card */
--muted: hsl(0 0% 14.9%)              /* Dark muted */
--border: hsl(0 0% 14.9%)             /* Dark border */
```

### Typography

#### Font Families
- **Heading**: Outfit (Google Fonts)
- **Body**: Inter (Google Fonts)

#### Font Sizes & Weights
```css
/* Headings */
h1: text-lg font-bold (18px, 700)
h2: text-base font-semibold (16px, 600)
h3: text-sm font-medium (14px, 500)

/* Body Text */
body: text-sm (14px, 400)
small: text-xs (12px, 400)
caption: text-xs text-muted-foreground (12px, 400)
```

### Spacing System

#### Padding/Margin Scale
```css
0.5 = 2px    /* Micro spacing */
1   = 4px    /* Tiny spacing */
1.5 = 6px    /* Small spacing */
2   = 8px    /* Base spacing */
3   = 12px   /* Medium spacing */
4   = 16px   /* Large spacing */
6   = 24px   /* XL spacing */
8   = 32px   /* XXL spacing */
```

#### Border Radius
```css
--radius: 0.6rem (9.6px)
--radius-sm: calc(var(--radius) - 4px) = 5.6px
--radius-md: calc(var(--radius) - 2px) = 7.6px
--radius-lg: var(--radius) = 9.6px
--radius-xl: calc(var(--radius) + 4px) = 13.6px
```

## 🧩 Component Architecture

### Component Organization

#### Folder Structure
```
components/
├── ui/              # Base UI components (Button, Input, Card...)
├── layout/          # Layout components (Nav, Footer...)
├── shared/          # Shared business components
├── [feature]/       # Feature-specific components
└── theme/           # Theme-related components
```

#### Naming Conventions
- **Components**: PascalCase (`SubmitProjectForm`)
- **Files**: kebab-case (`submit-project-form.tsx`)
- **Props**: camelCase (`isLoading`, `onSubmit`)
- **CSS Classes**: Tailwind utilities

### Component Patterns

#### 1. Compound Components
```tsx
// ✅ Good - Compound pattern
<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>Content</CardContent>
  <CardFooter>Footer</CardFooter>
</Card>
```

#### 2. Variant-based Design
```tsx
// ✅ Good - Using class-variance-authority
const buttonVariants = cva(
  "base-classes",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground",
        destructive: "bg-destructive text-white",
        outline: "border border-input bg-background",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 px-3",
        lg: "h-10 px-6",
      },
    },
  }
)
```

#### 3. Polymorphic Components
```tsx
// ✅ Good - asChild pattern for flexibility
<Button asChild>
  <Link href="/dashboard">Go to Dashboard</Link>
</Button>
```

### Props Patterns

#### Required vs Optional Props
```tsx
interface ComponentProps {
  // Required props - no default values
  id: string
  name: string
  
  // Optional props - with defaults or undefined
  variant?: "default" | "outline"
  size?: "sm" | "md" | "lg"
  className?: string
  children?: React.ReactNode
}
```

#### Event Handler Naming
```tsx
interface FormProps {
  onSubmit: (data: FormData) => void
  onChange: (value: string) => void
  onError: (error: Error) => void
  onSuccess: () => void
}
```

## 📱 Responsive Design

### Breakpoints
```css
sm: 640px   /* Small devices */
md: 768px   /* Medium devices */
lg: 1024px  /* Large devices */
xl: 1280px  /* Extra large devices */
```

### Mobile-First Approach
```tsx
// ✅ Good - Mobile-first responsive classes
<div className="flex flex-col gap-4 md:flex-row md:gap-6">
  <div className="w-full md:w-1/2">Content</div>
</div>
```

### Container Patterns
```tsx
// ✅ Standard container pattern
<div className="container mx-auto max-w-6xl px-4">
  {/* Content */}
</div>
```

## 🎯 UX Patterns

### Form Design

#### Multi-step Forms
```tsx
// ✅ Good - Step indicator with progress
const renderStepper = () => (
  <div className="mb-8 sm:mb-10">
    <div className="flex items-center justify-between">
      {steps.map((step, index) => (
        <StepIndicator 
          key={step.id}
          step={step}
          isActive={currentStep === index}
          isCompleted={currentStep > index}
        />
      ))}
    </div>
    <ProgressBar progress={(currentStep / totalSteps) * 100} />
  </div>
)
```

#### Form Validation
```tsx
// ✅ Good - Immediate feedback with clear error states
<Input
  {...register("email")}
  className={errors.email ? "border-destructive" : ""}
  aria-invalid={!!errors.email}
/>
{errors.email && (
  <p className="text-destructive text-xs mt-1">
    {errors.email.message}
  </p>
)}
```

### Loading States

#### Button Loading
```tsx
// ✅ Good - Clear loading state with spinner
<Button disabled={isLoading}>
  {isLoading ? (
    <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
  ) : (
    <RiRocketLine className="mr-2 h-4 w-4" />
  )}
  {isLoading ? "Submitting..." : "Submit Project"}
</Button>
```

#### Content Loading
```tsx
// ✅ Good - Skeleton loading for content
{isLoading ? (
  <div className="space-y-3">
    <Skeleton className="h-4 w-full" />
    <Skeleton className="h-4 w-3/4" />
    <Skeleton className="h-4 w-1/2" />
  </div>
) : (
  <Content />
)}
```

### Error Handling

#### Error Display
```tsx
// ✅ Good - Consistent error styling
{error && (
  <div className="bg-destructive/10 border-destructive/30 text-destructive rounded-md border p-3 text-sm">
    {error}
  </div>
)}
```

#### Toast Notifications
```tsx
// ✅ Good - Using sonner for notifications
import { toast } from "sonner"

const handleSuccess = () => {
  toast.success("Project submitted successfully!")
}

const handleError = (error: string) => {
  toast.error(error || "An error occurred")
}
```

## 🎨 Visual Hierarchy

### Card Design
```tsx
// ✅ Good - Consistent card pattern
<div className="bg-card border-border overflow-hidden rounded-lg border transition-all duration-200 hover:shadow-md">
  <div className="p-6">
    <h3 className="font-semibold mb-2">Title</h3>
    <p className="text-muted-foreground text-sm">Description</p>
  </div>
</div>
```

### Badge System
```tsx
// ✅ Good - Semantic badge variants
<Badge variant="default">Primary</Badge>
<Badge variant="secondary">Secondary</Badge>
<Badge variant="outline">Outline</Badge>
<Badge variant="destructive">Error</Badge>
```

### Icon Usage
```tsx
// ✅ Good - Consistent icon sizing and positioning
<Button>
  <RiRocketLine className="mr-2 h-4 w-4" />
  Launch Project
</Button>
```

## ♿ Accessibility

### ARIA Labels
```tsx
// ✅ Good - Proper ARIA attributes
<Button
  aria-label="Remove logo"
  aria-describedby="logo-help-text"
>
  <RiCloseCircleLine className="h-5 w-5" />
</Button>
```

### Focus Management
```tsx
// ✅ Good - Visible focus states
className="focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]"
```

### Semantic HTML
```tsx
// ✅ Good - Proper semantic structure
<nav aria-label="Main navigation">
  <ul role="list">
    <li><Link href="/">Home</Link></li>
    <li><Link href="/about">About</Link></li>
  </ul>
</nav>
```

## 🔧 Development Guidelines

### CSS Utility Classes
```tsx
// ✅ Good - Tailwind utility classes
className="flex items-center justify-between gap-4 rounded-md border p-4"

// ❌ Avoid - Custom CSS when utilities exist
style={{ display: 'flex', alignItems: 'center' }}
```

### Class Merging
```tsx
// ✅ Good - Using cn() utility for class merging
import { cn } from "@/lib/utils"

<div className={cn("base-classes", variant && "variant-classes", className)} />
```

### Performance Optimization
```tsx
// ✅ Good - Lazy loading images
<Image
  src={logoUrl}
  alt={`${name} logo`}
  fill
  className="object-contain"
  sizes="(max-width: 640px) 48px, 56px"
/>
```

## 📏 Spacing Guidelines

### Component Spacing
- **Internal padding**: `p-3` (12px) mobile, `p-4` (16px) desktop
- **Gap between elements**: `gap-2` (8px) small, `gap-4` (16px) medium
- **Section spacing**: `space-y-6` (24px) mobile, `space-y-8` (32px) desktop

### Layout Spacing
- **Container padding**: `px-4` (16px) mobile, `px-6` (24px) desktop
- **Section margins**: `mb-8` (32px) mobile, `mb-10` (40px) desktop

## 🎭 Animation Guidelines

### Transitions
```css
/* Standard transition */
transition-all duration-200

/* Specific property transitions */
transition-[color,box-shadow] duration-300

/* Hover effects */
hover:bg-accent hover:text-accent-foreground
```

### Loading Animations
```tsx
// ✅ Good - Spin animation for loaders
<RiLoader4Line className="h-4 w-4 animate-spin" />

// ✅ Good - Pulse animation for skeletons
<div className="animate-pulse bg-muted h-4 w-full rounded" />
```

## 🚀 Performance Best Practices

### Image Optimization
- Sử dụng Next.js Image component
- Định nghĩa sizes attribute cho responsive images
- Lazy loading mặc định

### Bundle Optimization
- Tree-shaking với ES modules
- Dynamic imports cho code splitting
- Minimize CSS với Tailwind purge

### Accessibility Performance
- Prefers-reduced-motion support
- High contrast mode support
- Screen reader optimization

---

## 📝 Checklist Tuân Thủ

### Trước khi commit:
- [ ] Component tuân thủ naming conventions
- [ ] Props interface được định nghĩa rõ ràng
- [ ] Responsive design hoạt động trên tất cả breakpoints
- [ ] Accessibility attributes được thêm đầy đủ
- [ ] Error states và loading states được xử lý
- [ ] Dark mode compatibility
- [ ] Performance optimization (images, lazy loading)
- [ ] TypeScript types chính xác
- [ ] CSS classes sử dụng design tokens
- [ ] Component documentation (nếu cần)

### Code Review:
- [ ] Kiểm tra tính nhất quán với design system
- [ ] Đảm bảo UX patterns được tuân thủ
- [ ] Verify accessibility compliance
- [ ] Performance impact assessment
- [ ] Mobile responsiveness testing

---

## 🔍 Component-Specific Guidelines

### Form Components

#### Input Fields
```tsx
// ✅ Good - Consistent input styling
<div className="space-y-2">
  <Label htmlFor="email">
    Email <span className="text-red-500">*</span>
  </Label>
  <Input
    id="email"
    type="email"
    placeholder="<EMAIL>"
    required
    aria-describedby="email-error"
  />
  {error && (
    <p id="email-error" className="text-destructive text-xs">
      {error.message}
    </p>
  )}
</div>
```

#### Select Components
```tsx
// ✅ Good - Grouped select with clear labeling
<Select onValueChange={setValue} value={value}>
  <SelectTrigger>
    <SelectValue placeholder="Select an option" />
  </SelectTrigger>
  <SelectContent>
    <SelectGroup>
      <SelectLabel>Category</SelectLabel>
      {options.map((option) => (
        <SelectItem key={option.id} value={option.value}>
          {option.label}
        </SelectItem>
      ))}
    </SelectGroup>
  </SelectContent>
</Select>
```

#### Checkbox & Radio Groups
```tsx
// ✅ Good - Accessible checkbox group
<div className="space-y-3">
  <Label className="text-base font-medium">
    Categories <span className="text-red-500">*</span>
  </Label>
  <div className="space-y-2">
    {categories.map((category) => (
      <div key={category.id} className="flex items-center space-x-2">
        <Checkbox
          id={`cat-${category.id}`}
          checked={selectedCategories.includes(category.id)}
          onCheckedChange={(checked) =>
            handleCategoryChange(category.id, !!checked)
          }
        />
        <Label
          htmlFor={`cat-${category.id}`}
          className="cursor-pointer font-normal"
        >
          {category.name}
        </Label>
      </div>
    ))}
  </div>
</div>
```

### Navigation Components

#### Primary Navigation
```tsx
// ✅ Good - Accessible navigation with proper ARIA
<nav className="bg-background/95 border-border/40 sticky top-0 z-50 border-b backdrop-blur-sm">
  <div className="container mx-auto flex h-16 max-w-6xl items-center justify-between px-4">
    <Link href="/" className="font-heading flex items-center">
      <img src="/logo.svg" alt="Open Launch" className="mr-1 h-6 w-6" />
      <span className="text-lg font-bold">Open-Launch</span>
    </Link>

    <div className="hidden items-center gap-6 md:flex">
      <NavMenu />
      <UserActions />
    </div>

    <MobileMenu className="md:hidden" />
  </div>
</nav>
```

#### Mobile Navigation
```tsx
// ✅ Good - Mobile-first navigation drawer
<Sheet>
  <SheetTrigger asChild>
    <Button variant="ghost" size="icon" aria-label="Open menu">
      <RiMenuLine className="h-5 w-5" />
    </Button>
  </SheetTrigger>
  <SheetContent side="right">
    <SheetHeader>
      <SheetTitle>Navigation</SheetTitle>
    </SheetHeader>
    <nav className="mt-6">
      <ul className="space-y-2">
        {navItems.map((item) => (
          <li key={item.href}>
            <SheetClose asChild>
              <Link
                href={item.href}
                className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-muted transition-colors"
              >
                <item.icon className="h-4 w-4" />
                {item.label}
              </Link>
            </SheetClose>
          </li>
        ))}
      </ul>
    </nav>
  </SheetContent>
</Sheet>
```

### Data Display Components

#### Project Cards
```tsx
// ✅ Good - Consistent card layout with hover states
<div className="group cursor-pointer rounded-xl p-3 transition-colors hover:bg-zinc-50 dark:hover:bg-zinc-900/50">
  <div className="flex items-start gap-3 sm:gap-4">
    <div className="flex-shrink-0">
      <div className="relative h-12 w-12 overflow-hidden rounded-md">
        <Image
          src={logoUrl}
          alt={`${name} logo`}
          fill
          className="object-contain"
          sizes="48px"
        />
      </div>
    </div>

    <div className="min-w-0 flex-grow">
      <h3 className="group-hover:text-primary line-clamp-1 text-sm font-medium transition-colors">
        {name}
      </h3>
      <p className="text-muted-foreground line-clamp-2 text-xs">
        {description}
      </p>
      <div className="mt-2 flex flex-wrap gap-1">
        {categories.map((cat) => (
          <Badge key={cat.id} variant="secondary" className="text-xs">
            {cat.name}
          </Badge>
        ))}
      </div>
    </div>

    <ProjectActions projectId={id} />
  </div>
</div>
```

#### Tables
```tsx
// ✅ Good - Responsive table with proper headers
<div className="overflow-x-auto">
  <Table>
    <TableHeader>
      <TableRow>
        <TableHead className="w-[100px]">ID</TableHead>
        <TableHead>Name</TableHead>
        <TableHead>Status</TableHead>
        <TableHead className="text-right">Actions</TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      {data.map((item) => (
        <TableRow key={item.id}>
          <TableCell className="font-medium">{item.id}</TableCell>
          <TableCell>{item.name}</TableCell>
          <TableCell>
            <Badge variant={getStatusVariant(item.status)}>
              {item.status}
            </Badge>
          </TableCell>
          <TableCell className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <RiMoreLine className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Edit</DropdownMenuItem>
                <DropdownMenuItem>Delete</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  </Table>
</div>
```

## 🎨 Advanced Styling Patterns

### Conditional Styling
```tsx
// ✅ Good - Using cn() for conditional classes
<Button
  className={cn(
    "base-classes",
    isActive && "bg-primary text-primary-foreground",
    isDisabled && "opacity-50 cursor-not-allowed",
    size === "large" && "h-12 px-6",
    className
  )}
>
  {children}
</Button>
```

### State-based Styling
```tsx
// ✅ Good - Clear state indicators
<div
  className={cn(
    "rounded-lg border p-4 transition-all",
    {
      "border-primary ring-primary bg-primary/5 ring-1": isSelected,
      "border-destructive ring-destructive bg-destructive/5 ring-1": hasError,
      "border-muted-foreground/20 hover:border-muted-foreground/40": !isSelected && !hasError,
    }
  )}
>
  {content}
</div>
```

### Animation Patterns
```tsx
// ✅ Good - Consistent animation timing
<div className="transform transition-all duration-200 ease-out hover:-translate-y-1 hover:shadow-lg">
  {content}
</div>

// ✅ Good - Loading state animations
<div className="flex items-center gap-2">
  <div className="h-2 w-2 animate-pulse rounded-full bg-primary" />
  <div className="h-2 w-2 animate-pulse rounded-full bg-primary animation-delay-75" />
  <div className="h-2 w-2 animate-pulse rounded-full bg-primary animation-delay-150" />
</div>
```

## 🔧 Advanced Development Patterns

### Custom Hooks for UI State
```tsx
// ✅ Good - Reusable UI state hooks
function useFormStep(totalSteps: number) {
  const [currentStep, setCurrentStep] = useState(1)

  const nextStep = useCallback(() => {
    setCurrentStep(prev => Math.min(prev + 1, totalSteps))
  }, [totalSteps])

  const prevStep = useCallback(() => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }, [])

  const goToStep = useCallback((step: number) => {
    if (step >= 1 && step <= totalSteps) {
      setCurrentStep(step)
    }
  }, [totalSteps])

  return {
    currentStep,
    nextStep,
    prevStep,
    goToStep,
    isFirstStep: currentStep === 1,
    isLastStep: currentStep === totalSteps,
    progress: (currentStep / totalSteps) * 100
  }
}
```

### Error Boundary Pattern
```tsx
// ✅ Good - Error boundary for component isolation
class ComponentErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error }
  }

  componentDidCatch(error, errorInfo) {
    console.error('Component error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="bg-destructive/10 border-destructive/30 text-destructive rounded-md border p-4">
          <h3 className="font-medium">Something went wrong</h3>
          <p className="text-sm mt-1">Please try refreshing the page.</p>
        </div>
      )
    }

    return this.props.children
  }
}
```

### Compound Component Pattern
```tsx
// ✅ Good - Flexible compound components
const FormSection = ({ children, className, ...props }) => (
  <div className={cn("space-y-4", className)} {...props}>
    {children}
  </div>
)

const FormSectionHeader = ({ children, className, ...props }) => (
  <div className={cn("border-b pb-2", className)} {...props}>
    {children}
  </div>
)

const FormSectionTitle = ({ children, className, ...props }) => (
  <h3 className={cn("text-lg font-semibold", className)} {...props}>
    {children}
  </h3>
)

const FormSectionDescription = ({ children, className, ...props }) => (
  <p className={cn("text-muted-foreground text-sm", className)} {...props}>
    {children}
  </p>
)

// Usage
<FormSection>
  <FormSectionHeader>
    <FormSectionTitle>Project Information</FormSectionTitle>
    <FormSectionDescription>
      Basic details about your project
    </FormSectionDescription>
  </FormSectionHeader>
  <FormFields />
</FormSection>
```

## 📊 Performance Optimization

### Image Optimization
```tsx
// ✅ Good - Optimized image loading
<Image
  src={imageUrl}
  alt={altText}
  width={400}
  height={300}
  className="rounded-lg object-cover"
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  priority={isAboveFold}
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

### Code Splitting
```tsx
// ✅ Good - Lazy loading heavy components
const HeavyComponent = lazy(() => import('./HeavyComponent'))

function App() {
  return (
    <Suspense fallback={<ComponentSkeleton />}>
      <HeavyComponent />
    </Suspense>
  )
}
```

### Memoization Patterns
```tsx
// ✅ Good - Memoizing expensive calculations
const ExpensiveComponent = memo(({ data, filters }) => {
  const processedData = useMemo(() => {
    return data.filter(item =>
      filters.every(filter => filter.test(item))
    ).sort((a, b) => a.priority - b.priority)
  }, [data, filters])

  return <DataDisplay data={processedData} />
})
```

## 🔒 Security & Privacy Guidelines

### Input Sanitization
```tsx
// ✅ Good - Sanitize user input
import DOMPurify from 'dompurify'

const SafeHtmlDisplay = ({ content }) => {
  const sanitizedContent = useMemo(() =>
    DOMPurify.sanitize(content), [content]
  )

  return (
    <div
      dangerouslySetInnerHTML={{ __html: sanitizedContent }}
      className="prose prose-sm max-w-none"
    />
  )
}
```

### CSRF Protection
```tsx
// ✅ Good - Include CSRF tokens in forms
<form onSubmit={handleSubmit}>
  <input type="hidden" name="_token" value={csrfToken} />
  {/* Form fields */}
</form>
```

### Rate Limiting UI Feedback
```tsx
// ✅ Good - Show rate limit feedback
const [rateLimitError, setRateLimitError] = useState(null)

const handleAction = async () => {
  try {
    await performAction()
  } catch (error) {
    if (error.status === 429) {
      setRateLimitError("Too many requests. Please wait before trying again.")
    }
  }
}
```

## 🧪 Testing Guidelines

### Component Testing
```tsx
// ✅ Good - Comprehensive component tests
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { SubmitProjectForm } from './SubmitProjectForm'

describe('SubmitProjectForm', () => {
  it('should validate required fields', async () => {
    render(<SubmitProjectForm userId="123" />)

    const submitButton = screen.getByRole('button', { name: /submit/i })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/please fill in all required/i)).toBeInTheDocument()
    })
  })

  it('should progress through steps correctly', async () => {
    render(<SubmitProjectForm userId="123" />)

    // Fill first step
    fireEvent.change(screen.getByLabelText(/project name/i), {
      target: { value: 'Test Project' }
    })

    const nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)

    expect(screen.getByText(/step 2/i)).toBeInTheDocument()
  })
})
```

### Accessibility Testing
```tsx
// ✅ Good - Test accessibility features
import { axe, toHaveNoViolations } from 'jest-axe'

expect.extend(toHaveNoViolations)

it('should not have accessibility violations', async () => {
  const { container } = render(<Component />)
  const results = await axe(container)
  expect(results).toHaveNoViolations()
})
```

### Visual Regression Testing
```tsx
// ✅ Good - Storybook stories for visual testing
export default {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
}

export const Default = {
  args: {
    children: 'Button',
  },
}

export const AllVariants = () => (
  <div className="flex gap-4">
    <Button variant="default">Default</Button>
    <Button variant="outline">Outline</Button>
    <Button variant="destructive">Destructive</Button>
  </div>
)
```

## 📱 Mobile-Specific Guidelines

### Touch Targets
```tsx
// ✅ Good - Minimum 44px touch targets
<Button
  size="sm"
  className="min-h-[44px] min-w-[44px]" // Ensure minimum touch target
>
  <RiHeartLine className="h-4 w-4" />
</Button>
```

### Mobile Navigation
```tsx
// ✅ Good - Mobile-optimized navigation
<nav className="fixed bottom-0 left-0 right-0 bg-background border-t md:hidden">
  <div className="flex items-center justify-around py-2">
    {navItems.map((item) => (
      <Link
        key={item.href}
        href={item.href}
        className="flex flex-col items-center gap-1 p-2 min-w-[60px]"
      >
        <item.icon className="h-5 w-5" />
        <span className="text-xs">{item.label}</span>
      </Link>
    ))}
  </div>
</nav>
```

### Responsive Typography
```tsx
// ✅ Good - Responsive text sizing
<h1 className="text-2xl font-bold md:text-3xl lg:text-4xl">
  Responsive Heading
</h1>

<p className="text-sm md:text-base leading-relaxed">
  Responsive body text with comfortable line height
</p>
```

## 🌐 Internationalization (i18n)

### Text Externalization
```tsx
// ✅ Good - Externalized strings
import { useTranslation } from 'next-i18next'

const Component = () => {
  const { t } = useTranslation('common')

  return (
    <div>
      <h1>{t('welcome.title')}</h1>
      <p>{t('welcome.description')}</p>
      <Button>{t('actions.submit')}</Button>
    </div>
  )
}
```

### RTL Support
```tsx
// ✅ Good - RTL-aware styling
<div className="flex items-center gap-2 rtl:flex-row-reverse">
  <Icon className="h-4 w-4" />
  <span>{text}</span>
</div>
```

## 🎯 SEO & Meta Guidelines

### Page Metadata
```tsx
// ✅ Good - Comprehensive metadata
export const metadata: Metadata = {
  title: 'Page Title | Open Launch',
  description: 'Detailed page description for SEO',
  keywords: ['keyword1', 'keyword2', 'keyword3'],
  openGraph: {
    title: 'Page Title',
    description: 'Social media description',
    images: ['/og-image.jpg'],
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Page Title',
    description: 'Twitter description',
    images: ['/twitter-image.jpg'],
  },
}
```

### Structured Data
```tsx
// ✅ Good - JSON-LD structured data
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Product",
  "name": projectName,
  "description": projectDescription,
  "url": projectUrl,
  "image": projectImage,
}

return (
  <>
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
    <Component />
  </>
)
```

## 🔄 State Management Patterns

### Local State
```tsx
// ✅ Good - useState for simple local state
const [isOpen, setIsOpen] = useState(false)
const [formData, setFormData] = useState(initialData)
```

### Complex State with useReducer
```tsx
// ✅ Good - useReducer for complex state logic
const formReducer = (state, action) => {
  switch (action.type) {
    case 'SET_FIELD':
      return { ...state, [action.field]: action.value }
    case 'SET_ERROR':
      return { ...state, errors: { ...state.errors, [action.field]: action.error } }
    case 'RESET':
      return initialState
    default:
      return state
  }
}

const [state, dispatch] = useReducer(formReducer, initialState)
```

### Global State (Zustand)
```tsx
// ✅ Good - Zustand store for global state
import { create } from 'zustand'

const useAuthStore = create((set) => ({
  user: null,
  isAuthenticated: false,
  login: (user) => set({ user, isAuthenticated: true }),
  logout: () => set({ user: null, isAuthenticated: false }),
}))
```

## 📈 Analytics & Monitoring

### Event Tracking
```tsx
// ✅ Good - Consistent event tracking
const trackEvent = (eventName: string, properties?: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, properties)
  }
}

const handleSubmit = () => {
  trackEvent('project_submitted', {
    project_type: formData.launchType,
    categories: formData.categories.length,
  })
}
```

### Error Monitoring
```tsx
// ✅ Good - Error boundary with monitoring
const logError = (error: Error, errorInfo: any) => {
  if (process.env.NODE_ENV === 'production') {
    // Send to monitoring service (Sentry, LogRocket, etc.)
    console.error('Component Error:', error, errorInfo)
  }
}
```

## 🚀 Deployment & Build Guidelines

### Environment Variables
```tsx
// ✅ Good - Type-safe environment variables
const config = {
  apiUrl: process.env.NEXT_PUBLIC_API_URL!,
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
}
```

### Build Optimization
```tsx
// ✅ Good - Bundle analysis and optimization
// next.config.js
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

module.exports = withBundleAnalyzer({
  experimental: {
    optimizeCss: true,
  },
  images: {
    formats: ['image/webp', 'image/avif'],
  },
})
```

---

## 📋 Final Checklist

### Pre-Development
- [ ] Design mockups reviewed and approved
- [ ] Component API designed and documented
- [ ] Accessibility requirements identified
- [ ] Performance requirements defined

### During Development
- [ ] Follow naming conventions
- [ ] Implement responsive design
- [ ] Add proper TypeScript types
- [ ] Include error handling
- [ ] Add loading states
- [ ] Implement accessibility features
- [ ] Write component tests

### Pre-Commit
- [ ] Code review completed
- [ ] Tests passing
- [ ] Accessibility tested
- [ ] Performance impact assessed
- [ ] Documentation updated
- [ ] Design system compliance verified

### Pre-Deployment
- [ ] Cross-browser testing
- [ ] Mobile device testing
- [ ] Performance audit
- [ ] Security review
- [ ] SEO optimization
- [ ] Analytics implementation

---

*Tài liệu này là living document và sẽ được cập nhật thường xuyên để phản ánh các thay đổi trong design system, công nghệ mới và best practices từ cộng đồng.*
