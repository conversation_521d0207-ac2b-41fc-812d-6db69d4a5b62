/**
 * Basic Test Setup Verification
 */

import { describe, it, expect, vi } from 'vitest'

describe('Basic Test Setup', () => {
  it('should have vitest working', () => {
    expect(1 + 1).toBe(2)
  })

  it('should have vi mocking working', () => {
    const mockFn = vi.fn()
    mockFn('test')
    expect(mockFn).toHaveBeenCalledWith('test')
  })

  it('should have environment variables', () => {
    expect(process.env.AWS_S3_REGION).toBeDefined()
    expect(process.env.AWS_S3_BUCKET_NAME).toBeDefined()
  })
})
