---
description:
globs:
alwaysApply: false
---
# API Routes

Open-Launch sử dụng API routes của Next.js để xử lý các yêu cầu từ client. Các API routes được định nghĩa trong thư mục [app/api](mdc:app/api/).

## Cấu trúc API Routes

- `app/api/auth/`: API routes liên quan đến xác thực
- `app/api/projects/`: API routes liên quan đến dự án
- `app/api/comments/`: API routes liên quan đến bình luận
- `app/api/search/`: API route cho tìm kiếm
- `app/api/cron/`: API routes cho các tác vụ định kỳ
- `app/api/payment/`: API routes liên quan đến thanh toán
- `app/api/uploadthing/`: API routes cho tải lên tệp

## API Route chính

- [app/api/auth/[...all]/route.ts](mdc:app/api/auth/[...all]/route.ts): API route xử lý xác thực
- [app/api/projects/[projectId]/status/route.ts](mdc:app/api/projects/[projectId]/status/route.ts): API route cập nhật trạng thái dự án
- [app/api/comments/[[...comment]]/route.ts](mdc:app/api/comments/[[...comment]]/route.ts): API route xử lý bình luận
- [app/api/search/route.ts](mdc:app/api/search/route.ts): API route xử lý tìm kiếm
- [app/api/uploadthing/route.ts](mdc:app/api/uploadthing/route.ts): API route xử lý tải lên tệp

## Cron Jobs

- [app/api/cron/update-launches/route.ts](mdc:app/api/cron/update-launches/route.ts): Cập nhật trạng thái ra mắt sản phẩm
- [app/api/cron/send-winner-notifications/route.ts](mdc:app/api/cron/send-winner-notifications/route.ts): Gửi thông báo cho người chiến thắng
- [app/api/cron/send-ongoing-reminders/route.ts](mdc:app/api/cron/send-ongoing-reminders/route.ts): Gửi nhắc nhở cho các ra mắt đang diễn ra

## Webhooks

- [app/api/auth/stripe/webhook/route.ts](mdc:app/api/auth/stripe/webhook/route.ts): Webhook xử lý các sự kiện từ Stripe
