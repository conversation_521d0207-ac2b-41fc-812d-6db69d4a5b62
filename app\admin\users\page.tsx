import { RiUserLine } from "@remixicon/react"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

export default function AdminUsersPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <RiUserLine className="h-6 w-6" />
        <h1 className="text-2xl font-bold">Quản lý Users</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Users Management</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Trang quản lý người dùng đang được phát triển...
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
