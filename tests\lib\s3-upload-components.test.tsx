/**
 * S3 Upload Components Unit Tests
 * 
 * Tests for lib/s3-upload-components.tsx
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { S3UploadButton, S3UploadDropzone } from '@/lib/s3-upload-components'

// Mock fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('S3UploadButton', () => {
  const mockOnUploadBegin = vi.fn()
  const mockOnClientUploadComplete = vi.fn()
  const mockOnUploadError = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    // Reset fetch mock
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: {
          url: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
          key: 'test-file.jpg',
          size: 1024,
          contentType: 'image/jpeg',
          uploadedBy: 'user123',
          fileUrl: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
        }
      })
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Rendering', () => {
    it('should render upload button', () => {
      render(
        <S3UploadButton
          endpoint="logo"
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      expect(screen.getByRole('button')).toBeInTheDocument()
      expect(screen.getByText('Upload Logo')).toBeInTheDocument()
    })

    it('should render product image button', () => {
      render(
        <S3UploadButton
          endpoint="product-image"
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      expect(screen.getByText('Add Product Image')).toBeInTheDocument()
    })

    it('should support UploadThing endpoint format', () => {
      render(
        <S3UploadButton
          endpoint="projectLogo"
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      expect(screen.getByText('Upload Logo')).toBeInTheDocument()
    })

    it('should render custom content', () => {
      render(
        <S3UploadButton
          endpoint="logo"
          content={{
            button: ({ ready, isUploading }) => (
              isUploading ? 'Uploading...' : 'Custom Upload Text'
            )
          }}
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      expect(screen.getByText('Custom Upload Text')).toBeInTheDocument()
    })

    it('should apply custom className', () => {
      render(
        <S3UploadButton
          endpoint="logo"
          className="custom-class"
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      const button = screen.getByRole('button')
      expect(button).toHaveClass('custom-class')
    })

    it('should apply custom appearance', () => {
      render(
        <S3UploadButton
          endpoint="logo"
          appearance={{
            button: 'custom-appearance-class'
          }}
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      const button = screen.getByRole('button')
      expect(button).toHaveClass('custom-appearance-class')
    })
  })

  describe('File Upload Flow', () => {
    it('should handle successful file upload', async () => {
      const user = userEvent.setup()
      
      render(
        <S3UploadButton
          endpoint="logo"
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      const button = screen.getByRole('button')
      await user.click(button)

      // Find the hidden file input
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      expect(fileInput).toBeInTheDocument()

      // Create a test file
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      
      // Simulate file selection
      await user.upload(fileInput, file)

      // Wait for upload to complete
      await waitFor(() => {
        expect(mockOnUploadBegin).toHaveBeenCalledTimes(1)
      })

      await waitFor(() => {
        expect(mockOnClientUploadComplete).toHaveBeenCalledTimes(1)
      })

      expect(mockOnClientUploadComplete).toHaveBeenCalledWith([{
        serverData: {
          fileUrl: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
          uploadedBy: 'user123'
        },
        url: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
        key: 'test-file.jpg',
        size: 1024,
        type: 'image/jpeg'
      }])
    })

    it('should handle upload error', async () => {
      const user = userEvent.setup()
      
      // Mock fetch to return error
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({
          success: false,
          error: 'Upload failed',
          message: 'File too large'
        })
      })

      render(
        <S3UploadButton
          endpoint="logo"
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      const button = screen.getByRole('button')
      await user.click(button)

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      
      await user.upload(fileInput, file)

      await waitFor(() => {
        expect(mockOnUploadError).toHaveBeenCalledTimes(1)
      })

      expect(mockOnUploadError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('File too large')
        })
      )
    })

    it('should handle network error', async () => {
      const user = userEvent.setup()
      
      // Mock fetch to throw network error
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      render(
        <S3UploadButton
          endpoint="logo"
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      const button = screen.getByRole('button')
      await user.click(button)

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      
      await user.upload(fileInput, file)

      await waitFor(() => {
        expect(mockOnUploadError).toHaveBeenCalledTimes(1)
      })

      expect(mockOnUploadError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Network error'
        })
      )
    })
  })

  describe('Button States', () => {
    it('should show loading state during upload', async () => {
      const user = userEvent.setup()
      
      // Mock fetch to delay response
      mockFetch.mockImplementationOnce(() =>
        new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: {
              url: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
              key: 'test-file.jpg',
              size: 1024,
              contentType: 'image/jpeg',
              uploadedBy: 'user123',
              fileUrl: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
            }
          })
        }), 100))
      )

      render(
        <S3UploadButton
          endpoint="logo"
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      const button = screen.getByRole('button')
      await user.click(button)

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      
      await user.upload(fileInput, file)

      // Should show loading state
      await waitFor(() => {
        expect(screen.getByRole('button')).toHaveClass('opacity-50')
      })
    })

    it('should be disabled when disabled prop is true', () => {
      render(
        <S3UploadButton
          endpoint="logo"
          disabled={true}
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
      expect(button).toHaveClass('opacity-50')
    })
  })

  describe('Endpoint Mapping', () => {
    it('should map logo endpoint correctly', async () => {
      const user = userEvent.setup()
      
      render(
        <S3UploadButton
          endpoint="logo"
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      const button = screen.getByRole('button')
      await user.click(button)

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      
      await user.upload(fileInput, file)

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          '/api/upload/logo',
          expect.objectContaining({
            method: 'POST',
            body: expect.any(FormData)
          })
        )
      })
    })

    it('should map UploadThing projectLogo endpoint correctly', async () => {
      const user = userEvent.setup()
      
      render(
        <S3UploadButton
          endpoint="projectLogo"
          onUploadBegin={mockOnUploadBegin}
          onClientUploadComplete={mockOnClientUploadComplete}
          onUploadError={mockOnUploadError}
        />
      )

      const button = screen.getByRole('button')
      await user.click(button)

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      
      await user.upload(fileInput, file)

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          '/api/upload/logo',
          expect.objectContaining({
            method: 'POST'
          })
        )
      })
    })
  })
})

describe('S3UploadDropzone', () => {
  it('should render placeholder dropzone', () => {
    render(<S3UploadDropzone />)
    
    expect(screen.getByText('S3UploadDropzone - Coming Soon')).toBeInTheDocument()
    expect(screen.getByText('Use S3UploadButton for now')).toBeInTheDocument()
  })
})
