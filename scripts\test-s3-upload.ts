#!/usr/bin/env tsx

/**
 * S3 Upload System Test Script
 * 
 * Comprehensive testing của toàn bộ S3 upload functionality
 * Usage: bun run test:s3-upload
 */

import { config } from 'dotenv'
import fs from 'fs'
import path from 'path'
import { uploadToS3, deleteFromS3, fileExistsInS3, generateS3Key } from '../lib/s3-upload'
import { validateImageFile, generateFileKey } from '../lib/file-validation'

// Load environment variables
config()

interface TestResult {
  name: string
  status: 'PASS' | 'FAIL' | 'SKIP'
  message: string
  duration?: number
}

class S3UploadTester {
  private results: TestResult[] = []
  private testFiles: { [key: string]: Buffer } = {}

  constructor() {
    this.setupTestFiles()
  }

  private setupTestFiles() {
    // Tạo test image files
    this.testFiles.validJpeg = this.createTestImageBuffer('jpeg', 1024) // 1KB
    this.testFiles.validPng = this.createTestImageBuffer('png', 2048)   // 2KB
    this.testFiles.largeFile = this.createTestImageBuffer('jpeg', 1024 * 1024 * 2) // 2MB (too large)
    this.testFiles.emptyFile = Buffer.alloc(0) // Empty file
  }

  private createTestImageBuffer(type: string, size: number): Buffer {
    // Tạo minimal valid image header
    const header = type === 'jpeg' 
      ? Buffer.from([0xFF, 0xD8, 0xFF, 0xE0]) // JPEG header
      : Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]) // PNG header
    
    const padding = Buffer.alloc(Math.max(0, size - header.length))
    return Buffer.concat([header, padding])
  }

  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now()
    
    try {
      await testFn()
      const duration = Date.now() - startTime
      this.results.push({
        name,
        status: 'PASS',
        message: 'Test passed successfully',
        duration
      })
    } catch (error) {
      const duration = Date.now() - startTime
      this.results.push({
        name,
        status: 'FAIL',
        message: error instanceof Error ? error.message : 'Unknown error',
        duration
      })
    }
  }

  async testFileValidation() {
    console.log('\n📋 Testing File Validation...')

    await this.runTest('Valid JPEG file validation', async () => {
      const file = new File([this.testFiles.validJpeg], 'test.jpg', { type: 'image/jpeg' })
      const error = validateImageFile(file, 'logo')
      if (error) throw new Error(`Validation failed: ${error.message}`)
    })

    await this.runTest('Valid PNG file validation', async () => {
      const file = new File([this.testFiles.validPng], 'test.png', { type: 'image/png' })
      const error = validateImageFile(file, 'product-image')
      if (error) throw new Error(`Validation failed: ${error.message}`)
    })

    await this.runTest('Large file rejection', async () => {
      const file = new File([this.testFiles.largeFile], 'large.jpg', { type: 'image/jpeg' })
      const error = validateImageFile(file, 'logo')
      if (!error || error.code !== 'FILE_TOO_LARGE') {
        throw new Error('Large file should be rejected')
      }
    })

    await this.runTest('Empty file rejection', async () => {
      const file = new File([this.testFiles.emptyFile], 'empty.jpg', { type: 'image/jpeg' })
      const error = validateImageFile(file, 'logo')
      if (!error || error.code !== 'FILE_CORRUPTED') {
        throw new Error('Empty file should be rejected')
      }
    })

    await this.runTest('Invalid file type rejection', async () => {
      const file = new File([this.testFiles.validJpeg], 'test.txt', { type: 'text/plain' })
      const error = validateImageFile(file, 'logo')
      if (!error || error.code !== 'INVALID_FILE_TYPE') {
        throw new Error('Invalid file type should be rejected')
      }
    })
  }

  async testS3Operations() {
    console.log('\n☁️ Testing S3 Operations...')

    const testUserId = 'test-user-' + Date.now()
    let uploadedKey: string | null = null

    await this.runTest('S3 key generation', async () => {
      const key = generateS3Key(testUserId, 'logo', 'jpg')
      if (!key.includes(testUserId) || !key.includes('logo')) {
        throw new Error('Generated key is invalid')
      }
    })

    await this.runTest('File upload to S3', async () => {
      const key = generateS3Key(testUserId, 'logo', 'jpg')
      const result = await uploadToS3(this.testFiles.validJpeg, key, 'image/jpeg')
      
      if (!result.url || !result.key || result.size !== this.testFiles.validJpeg.length) {
        throw new Error('Upload result is invalid')
      }
      
      uploadedKey = result.key
    })

    await this.runTest('File existence check', async () => {
      if (!uploadedKey) throw new Error('No uploaded file to check')
      
      const exists = await fileExistsInS3(uploadedKey)
      if (!exists) {
        throw new Error('Uploaded file should exist in S3')
      }
    })

    await this.runTest('File deletion from S3', async () => {
      if (!uploadedKey) throw new Error('No uploaded file to delete')
      
      await deleteFromS3(uploadedKey)
      
      // Verify deletion
      const exists = await fileExistsInS3(uploadedKey)
      if (exists) {
        throw new Error('File should be deleted from S3')
      }
    })
  }

  async testUtilityFunctions() {
    console.log('\n🔧 Testing Utility Functions...')

    await this.runTest('File key generation', async () => {
      const file = new File([this.testFiles.validJpeg], 'test.jpg', { type: 'image/jpeg' })
      const key = generateFileKey('test-user', 'logo', file)
      
      if (!key.includes('test-user') || !key.includes('logo') || !key.endsWith('.jpg')) {
        throw new Error('Generated file key is invalid')
      }
    })

    await this.runTest('File extension detection', async () => {
      const { getFileExtension } = await import('../lib/file-validation')
      
      const jpegFile = new File([this.testFiles.validJpeg], 'test.jpg', { type: 'image/jpeg' })
      const pngFile = new File([this.testFiles.validPng], 'test.png', { type: 'image/png' })
      
      if (getFileExtension(jpegFile) !== 'jpg') {
        throw new Error('JPEG extension detection failed')
      }
      
      if (getFileExtension(pngFile) !== 'png') {
        throw new Error('PNG extension detection failed')
      }
    })
  }

  async testErrorHandling() {
    console.log('\n❌ Testing Error Handling...')

    await this.runTest('Invalid S3 key handling', async () => {
      try {
        await uploadToS3(this.testFiles.validJpeg, '', 'image/jpeg')
        throw new Error('Should have thrown error for empty key')
      } catch (error) {
        if (!(error instanceof Error) || !error.message.includes('key')) {
          throw new Error('Wrong error type for invalid key')
        }
      }
    })

    await this.runTest('Empty buffer handling', async () => {
      try {
        await uploadToS3(Buffer.alloc(0), 'test-key', 'image/jpeg')
        throw new Error('Should have thrown error for empty buffer')
      } catch (error) {
        if (!(error instanceof Error) || !error.message.includes('empty')) {
          throw new Error('Wrong error type for empty buffer')
        }
      }
    })
  }

  async runAllTests() {
    console.log('🧪 Starting S3 Upload System Tests...')
    console.log('=' .repeat(50))

    try {
      await this.testFileValidation()
      await this.testS3Operations()
      await this.testUtilityFunctions()
      await this.testErrorHandling()
    } catch (error) {
      console.error('Test suite failed:', error)
    }

    this.printResults()
  }

  private printResults() {
    console.log('\n' + '='.repeat(50))
    console.log('📊 Test Results Summary')
    console.log('='.repeat(50))

    const passed = this.results.filter(r => r.status === 'PASS').length
    const failed = this.results.filter(r => r.status === 'FAIL').length
    const total = this.results.length

    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : '❌'
      const duration = result.duration ? ` (${result.duration}ms)` : ''
      console.log(`${icon} ${result.name}${duration}`)
      
      if (result.status === 'FAIL') {
        console.log(`   └─ ${result.message}`)
      }
    })

    console.log('\n' + '-'.repeat(50))
    console.log(`Total: ${total} | Passed: ${passed} | Failed: ${failed}`)
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`)

    if (failed > 0) {
      console.log('\n❌ Some tests failed. Please check the errors above.')
      process.exit(1)
    } else {
      console.log('\n🎉 All tests passed! S3 upload system is working correctly.')
    }
  }
}

// Run tests
if (require.main === module) {
  const tester = new S3UploadTester()
  tester.runAllTests().catch(console.error)
}
