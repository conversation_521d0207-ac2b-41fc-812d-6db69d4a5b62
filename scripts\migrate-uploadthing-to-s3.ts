#!/usr/bin/env tsx

/**
 * UploadThing to S3 Migration Script
 * 
 * Migrates existing UploadThing files to AWS S3
 * Usage: bun run s3:migrate
 */

import { config } from 'dotenv'
import { db } from '../drizzle/db'
import { project } from '../drizzle/db/schema'
import { uploadToS3, generateS3Key } from '../lib/s3-upload'
import { isNotNull } from 'drizzle-orm'

// Load environment variables
config()

interface MigrationStats {
  total: number
  processed: number
  migrated: number
  skipped: number
  failed: number
  errors: string[]
}

class UploadThingMigrator {
  private stats: MigrationStats = {
    total: 0,
    processed: 0,
    migrated: 0,
    skipped: 0,
    failed: 0,
    errors: []
  }

  private isUploadThingUrl(url: string): boolean {
    return url.includes('uploadthing') || 
           url.includes('ufs.sh') || 
           url.includes('utfs.io')
  }

  private async downloadFile(url: string): Promise<{ buffer: Buffer; contentType: string }> {
    const response = await fetch(url)
    
    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.status} ${response.statusText}`)
    }

    const buffer = Buffer.from(await response.arrayBuffer())
    const contentType = response.headers.get('content-type') || 'image/jpeg'

    return { buffer, contentType }
  }

  private generateMigrationKey(originalUrl: string, userId: string, type: 'logo' | 'product-image'): string {
    // Extract filename from original URL if possible
    const urlParts = originalUrl.split('/')
    const filename = urlParts[urlParts.length - 1]
    const extension = filename.includes('.') ? filename.split('.').pop() : 'jpg'
    
    return generateS3Key(userId, type, extension)
  }

  private async migrateFile(
    originalUrl: string, 
    userId: string, 
    type: 'logo' | 'product-image'
  ): Promise<string> {
    console.log(`   📥 Downloading: ${originalUrl}`)
    
    // Download file from UploadThing
    const { buffer, contentType } = await this.downloadFile(originalUrl)
    
    // Generate S3 key
    const s3Key = this.generateMigrationKey(originalUrl, userId, type)
    
    console.log(`   📤 Uploading to S3: ${s3Key}`)
    
    // Upload to S3
    const result = await uploadToS3(buffer, s3Key, contentType)
    
    console.log(`   ✅ Migrated: ${result.url}`)
    
    return result.url
  }

  async migrateProjectFiles() {
    console.log('🔄 Starting UploadThing to S3 Migration...')
    console.log('=' .repeat(60))

    try {
      // Get all projects with UploadThing URLs
      const projects = await db
        .select({
          id: project.id,
          name: project.name,
          logoUrl: project.logoUrl,
          productImage: project.productImage,
          createdBy: project.createdBy
        })
        .from(project)
        .where(isNotNull(project.logoUrl))

      this.stats.total = projects.length
      console.log(`📊 Found ${this.stats.total} projects to check\n`)

      for (const proj of projects) {
        this.stats.processed++
        
        console.log(`[${this.stats.processed}/${this.stats.total}] Processing: ${proj.name}`)
        
        const updates: Partial<typeof project.$inferInsert> = {}
        let hasUpdates = false

        try {
          // Migrate logo if it's from UploadThing
          if (proj.logoUrl && this.isUploadThingUrl(proj.logoUrl)) {
            console.log('  🖼️ Migrating logo...')
            updates.logoUrl = await this.migrateFile(
              proj.logoUrl, 
              proj.createdBy || 'unknown', 
              'logo'
            )
            hasUpdates = true
          } else if (proj.logoUrl) {
            console.log('  ⏭️ Logo already on S3, skipping')
          }

          // Migrate product image if it exists and is from UploadThing
          if (proj.productImage && this.isUploadThingUrl(proj.productImage)) {
            console.log('  🖼️ Migrating product image...')
            updates.productImage = await this.migrateFile(
              proj.productImage, 
              proj.createdBy || 'unknown', 
              'product-image'
            )
            hasUpdates = true
          } else if (proj.productImage) {
            console.log('  ⏭️ Product image already on S3, skipping')
          }

          // Update database if we have changes
          if (hasUpdates) {
            await db
              .update(project)
              .set(updates)
              .where(project.id.eq(proj.id))
            
            this.stats.migrated++
            console.log('  ✅ Database updated')
          } else {
            this.stats.skipped++
            console.log('  ⏭️ No migration needed')
          }

        } catch (error) {
          this.stats.failed++
          const errorMsg = `Failed to migrate project ${proj.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
          this.stats.errors.push(errorMsg)
          console.error(`  ❌ ${errorMsg}`)
        }

        console.log('') // Empty line for readability
      }

    } catch (error) {
      console.error('❌ Migration failed:', error)
      throw error
    }
  }

  async runMigration() {
    const startTime = Date.now()

    try {
      await this.migrateProjectFiles()
    } catch (error) {
      console.error('Migration process failed:', error)
      process.exit(1)
    }

    const duration = Date.now() - startTime
    this.printSummary(duration)
  }

  private printSummary(duration: number) {
    console.log('='.repeat(60))
    console.log('📊 Migration Summary')
    console.log('='.repeat(60))
    
    console.log(`⏱️  Duration: ${(duration / 1000).toFixed(2)}s`)
    console.log(`📁 Total projects: ${this.stats.total}`)
    console.log(`✅ Successfully migrated: ${this.stats.migrated}`)
    console.log(`⏭️  Skipped (already S3): ${this.stats.skipped}`)
    console.log(`❌ Failed: ${this.stats.failed}`)
    
    if (this.stats.errors.length > 0) {
      console.log('\n❌ Errors encountered:')
      this.stats.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`)
      })
    }

    const successRate = this.stats.total > 0 
      ? ((this.stats.migrated + this.stats.skipped) / this.stats.total * 100).toFixed(1)
      : '0'

    console.log(`\n📈 Success rate: ${successRate}%`)

    if (this.stats.failed === 0) {
      console.log('\n🎉 Migration completed successfully!')
    } else {
      console.log('\n⚠️  Migration completed with some failures. Please review the errors above.')
    }
  }

  async dryRun() {
    console.log('🔍 Running Migration Dry Run...')
    console.log('=' .repeat(60))

    const projects = await db
      .select({
        id: project.id,
        name: project.name,
        logoUrl: project.logoUrl,
        productImage: project.productImage,
        createdBy: project.createdBy
      })
      .from(project)
      .where(isNotNull(project.logoUrl))

    let uploadThingCount = 0
    let s3Count = 0

    console.log(`📊 Analyzing ${projects.length} projects...\n`)

    projects.forEach((proj, index) => {
      console.log(`[${index + 1}] ${proj.name}`)
      
      if (proj.logoUrl) {
        if (this.isUploadThingUrl(proj.logoUrl)) {
          console.log(`  📤 Logo: UploadThing → S3 (MIGRATE)`)
          uploadThingCount++
        } else {
          console.log(`  ✅ Logo: Already S3 (SKIP)`)
          s3Count++
        }
      }

      if (proj.productImage) {
        if (this.isUploadThingUrl(proj.productImage)) {
          console.log(`  📤 Product Image: UploadThing → S3 (MIGRATE)`)
          uploadThingCount++
        } else {
          console.log(`  ✅ Product Image: Already S3 (SKIP)`)
          s3Count++
        }
      }

      console.log('')
    })

    console.log('='.repeat(60))
    console.log('📊 Dry Run Summary')
    console.log('='.repeat(60))
    console.log(`📤 Files to migrate: ${uploadThingCount}`)
    console.log(`✅ Files already on S3: ${s3Count}`)
    console.log(`📁 Total projects: ${projects.length}`)
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2)
  const isDryRun = args.includes('--dry-run') || args.includes('-d')

  const migrator = new UploadThingMigrator()

  if (isDryRun) {
    await migrator.dryRun()
  } else {
    console.log('⚠️  This will migrate files from UploadThing to S3.')
    console.log('   Make sure you have proper S3 configuration and backups.')
    console.log('   Use --dry-run flag to preview changes first.\n')
    
    await migrator.runMigration()
  }
}

// Run migration
if (require.main === module) {
  main().catch(console.error)
}
