"use client"

import { useState, useEffect, useCallback } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useScreenshot, isValidUrl } from "@/hooks/use-screenshot"
import { RiCameraLine, RiRefreshLine, RiErrorWarningLine, RiCheckLine } from "@remixicon/react"
import { cn } from "@/lib/utils"

interface ScreenshotPreviewProps {
  websiteUrl: string
  onScreenshotGenerated: (imageUrl: string) => void
  className?: string
  disabled?: boolean
}

export function ScreenshotPreview({
  websiteUrl,
  onScreenshotGenerated,
  className,
  disabled = false,
}: ScreenshotPreviewProps) {
  const { state, generateScreenshot, getPreview, reset } = useScreenshot()
  const [showPreview, setShowPreview] = useState(false)

  // Reset state when <PERSON><PERSON> changes
  useEffect(() => {
    reset()
    setShowPreview(false)
  }, [websiteUrl, reset])

  // Auto-generate preview when URL is valid
  useEffect(() => {
    if (websiteUrl && isValidUrl(websiteUrl) && !showPreview && !state.loading) {
      handlePreview()
    }
  }, [websiteUrl, showPreview, state.loading, handlePreview])

  const handlePreview = useCallback(async () => {
    if (!websiteUrl || !isValidUrl(websiteUrl)) return

    setShowPreview(true)
    await getPreview(websiteUrl)
  }, [websiteUrl, getPreview])

  const handleGenerate = async () => {
    if (!websiteUrl || !isValidUrl(websiteUrl)) return

    const imageUrl = await generateScreenshot(websiteUrl)
    if (imageUrl) {
      onScreenshotGenerated(imageUrl)
    }
  }

  const handleRetry = () => {
    reset()
    handlePreview()
  }

  if (!websiteUrl || !isValidUrl(websiteUrl)) {
    return (
      <Card className={cn("border-dashed", className)}>
        <CardContent className="flex flex-col items-center justify-center py-8 text-center">
          <RiCameraLine className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-sm text-muted-foreground">
            Enter a valid website URL to preview screenshot
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-0">
        {/* Preview Section */}
        {showPreview && (
          <div className="relative">
            {state.loading && (
              <div className="flex items-center justify-center py-16 bg-muted/50">
                <div className="flex flex-col items-center gap-3">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <p className="text-sm text-muted-foreground">Generating preview...</p>
                </div>
              </div>
            )}

            {state.error && (
              <div className="flex flex-col items-center justify-center py-16 bg-destructive/5">
                <RiErrorWarningLine className="h-12 w-12 text-destructive mb-4" />
                <p className="text-sm text-destructive font-medium mb-2">Preview Failed</p>
                <p className="text-xs text-muted-foreground mb-4 max-w-sm text-center">
                  {state.error}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRetry}
                  disabled={disabled}
                >
                  <RiRefreshLine className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </div>
            )}

            {state.previewUrl && !state.loading && !state.error && (
              <div className="relative">
                <Image
                  src={state.previewUrl}
                  alt="Website preview"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover"
                  unoptimized // Since it's from external API
                />
                <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors" />
              </div>
            )}
          </div>
        )}

        {/* Action Section */}
        <div className="p-4 border-t bg-muted/30">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium mb-1">
                Auto-generate Product Image
              </p>
              <p className="text-xs text-muted-foreground">
                Create a screenshot from your website URL
              </p>
            </div>

            <div className="flex items-center gap-2">
              {!showPreview && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreview}
                  disabled={disabled || state.loading}
                >
                  <RiCameraLine className="h-4 w-4 mr-2" />
                  Preview
                </Button>
              )}

              {showPreview && state.previewUrl && (
                <Button
                  onClick={handleGenerate}
                  disabled={disabled || state.loading}
                  size="sm"
                >
                  {state.loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Generating...
                    </>
                  ) : state.imageUrl ? (
                    <>
                      <RiCheckLine className="h-4 w-4 mr-2" />
                      Generated
                    </>
                  ) : (
                    <>
                      <RiCameraLine className="h-4 w-4 mr-2" />
                      Generate
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>

          {/* Success Message */}
          {state.imageUrl && (
            <div className="mt-3 p-2 bg-green-50 dark:bg-green-950/20 rounded-md border border-green-200 dark:border-green-800">
              <div className="flex items-center gap-2">
                <RiCheckLine className="h-4 w-4 text-green-600" />
                <p className="text-sm text-green-700 dark:text-green-400">
                  Screenshot generated successfully! Image will be used as your product logo.
                </p>
              </div>
            </div>
          )}

          {/* Error Message */}
          {state.error && showPreview && (
            <div className="mt-3 p-2 bg-destructive/5 rounded-md border border-destructive/20">
              <div className="flex items-center gap-2">
                <RiErrorWarningLine className="h-4 w-4 text-destructive" />
                <p className="text-sm text-destructive">
                  {state.error}
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Compact version for inline use
 */
interface ScreenshotButtonProps {
  websiteUrl: string
  onScreenshotGenerated: (imageUrl: string) => void
  disabled?: boolean
  className?: string
}

export function ScreenshotButton({
  websiteUrl,
  onScreenshotGenerated,
  disabled = false,
  className,
}: ScreenshotButtonProps) {
  const { state, generateScreenshot } = useScreenshot()

  const handleGenerate = async () => {
    if (!websiteUrl || !isValidUrl(websiteUrl)) return

    const imageUrl = await generateScreenshot(websiteUrl)
    if (imageUrl) {
      onScreenshotGenerated(imageUrl)
    }
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleGenerate}
      disabled={disabled || state.loading || !isValidUrl(websiteUrl)}
      className={className}
    >
      {state.loading ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
          Generating...
        </>
      ) : (
        <>
          <RiCameraLine className="h-4 w-4 mr-2" />
          Auto Screenshot
        </>
      )}
    </Button>
  )
}
