# Admin Dashboard - Open Launch

## Tổng quan

Admin Dashboard là giao diện quản trị chuyên nghiệp cho phép admin quản lý toàn bộ hệ thống Open Launch, đặc biệt tập trung vào việc quản lý Projects.

## Tính năng chính

### 🚀 Quản lý Projects

#### Danh sách Projects
- **Hiển thị**: Table responsive với thông tin đầy đủ
- **Tìm kiếm**: <PERSON> t<PERSON>, mô tả, website URL
- **Lọc**: Theo status, launch type, category
- **Sắp xếp**: <PERSON> ng<PERSON>, tên, tr<PERSON><PERSON> thái, xếp hạng
- **Ph<PERSON> trang**: 5/10/20/50 items per page

#### Chi tiết Project
- **Thông tin cơ bản**: Tên, mô tả, logo, links
- **Metadata**: <PERSON><PERSON><PERSON>, cậ<PERSON> nhật, launch date
- **Categories**: Danh sách categories được gán
- **Tech Stack**: C<PERSON>ng nghệ sử dụng
- **Platforms**: Nền tảng hỗ trợ
- **Hình ảnh**: Cover image, product image

#### Chỉnh sửa Project
- **Form validation**: Sử dụng Zod schema
- **Multi-section form**: Chia thành các phần logic
- **Real-time validation**: Hiển thị lỗi ngay lập tức
- **Auto-save**: Tự động lưu khi có thay đổi

#### Bulk Actions
- **Select multiple**: Checkbox để chọn nhiều projects
- **Bulk status change**: Thay đổi trạng thái hàng loạt
- **Bulk featured**: Đánh dấu/bỏ đánh dấu featured
- **Bulk delete**: Xóa nhiều projects cùng lúc

### 🎯 Quick Actions

#### Project Actions
- **Toggle Featured**: Đánh dấu/bỏ đánh dấu featured
- **Change Status**: Scheduled, Launched, Draft, Cancelled
- **Edit**: Chỉnh sửa thông tin project
- **Delete**: Xóa project (có xác nhận)
- **View Website**: Mở website project

#### Navigation
- **Sidebar**: Navigation chính với icons
- **Breadcrumb**: Hiển thị vị trí hiện tại
- **Mobile responsive**: Sidebar collapse trên mobile

## Cấu trúc Components

### Core Components

```
components/admin/
├── admin-sidebar.tsx           # Sidebar navigation
├── admin-breadcrumb.tsx        # Breadcrumb navigation
├── admin-projects-content.tsx  # Main projects content
├── admin-projects-header.tsx   # Header with actions
├── admin-projects-filters.tsx  # Search & filters
├── admin-projects-table.tsx    # Projects table
├── admin-projects-pagination.tsx # Pagination
├── admin-bulk-actions.tsx      # Bulk actions bar
├── admin-project-details.tsx   # Project detail view
└── admin-project-edit-form.tsx # Edit form
```

### Server Actions

```
app/actions/admin.ts
├── getAdminProjects()          # Get projects with filters
├── getAdminProjectById()       # Get single project
├── updateAdminProject()        # Update project
├── deleteAdminProject()        # Delete project
├── bulkUpdateProjects()        # Bulk update
├── bulkDeleteProjects()        # Bulk delete
└── toggleProjectFeatured()     # Toggle featured
```

## Design System Compliance

### Colors & Typography
- Tuân thủ design tokens từ `design-rules.md`
- Sử dụng semantic colors cho status
- Typography scale nhất quán

### Components
- Sử dụng shadcn/ui components
- Compound component pattern
- Variant-based design với CVA

### Responsive Design
- Mobile-first approach
- Breakpoints: sm(640px), md(768px), lg(1024px)
- Touch-friendly targets (44px minimum)

### Accessibility
- ARIA labels đầy đủ
- Keyboard navigation
- Screen reader support
- Focus management

## Performance Optimizations

### Data Loading
- Server-side pagination
- Efficient database queries
- Parallel data fetching

### UI Performance
- Lazy loading components
- Memoization cho expensive calculations
- Optimized re-renders

### Images
- Next.js Image optimization
- Proper sizing attributes
- Lazy loading

## Security Features

### Authentication
- Admin role verification
- Session validation
- Protected routes

### Input Validation
- Zod schema validation
- Server-side validation
- XSS protection

### CSRF Protection
- Token validation
- Secure form submissions

## Usage Guide

### Truy cập Admin Dashboard

1. **Đăng nhập**: Với tài khoản có role "admin"
2. **Navigation**: Truy cập `/admin` hoặc click Admin Panel
3. **Projects**: Click "Projects" trong sidebar

### Quản lý Projects

#### Xem danh sách
1. Truy cập `/admin/projects`
2. Sử dụng search box để tìm kiếm
3. Áp dụng filters theo nhu cầu
4. Sắp xếp theo cột mong muốn

#### Chỉnh sửa project
1. Click "Chỉnh sửa" trong dropdown actions
2. Cập nhật thông tin cần thiết
3. Click "Lưu thay đổi"

#### Bulk actions
1. Chọn checkbox của các projects
2. Sử dụng bulk actions bar
3. Xác nhận thay đổi

### Best Practices

#### Performance
- Sử dụng pagination thay vì load tất cả
- Áp dụng filters để giảm data load
- Cache kết quả search khi có thể

#### UX
- Luôn hiển thị loading states
- Cung cấp feedback cho user actions
- Sử dụng confirmation cho destructive actions

#### Accessibility
- Test với screen readers
- Đảm bảo keyboard navigation
- Maintain focus management

## Troubleshooting

### Common Issues

#### Slow loading
- Check database indexes
- Optimize queries
- Reduce page size

#### Permission errors
- Verify admin role
- Check session validity
- Review authentication flow

#### Form validation errors
- Check Zod schemas
- Verify required fields
- Review error messages

### Debug Tools

#### Development
- React DevTools
- Network tab
- Console logs

#### Production
- Error monitoring
- Performance metrics
- User feedback

## Future Enhancements

### Planned Features
- [ ] Advanced analytics dashboard
- [ ] Bulk import/export
- [ ] Activity logs
- [ ] Advanced search with filters
- [ ] Real-time notifications
- [ ] Audit trail

### Performance Improvements
- [ ] Virtual scrolling for large lists
- [ ] Background sync
- [ ] Optimistic updates
- [ ] Better caching strategies

---

*Tài liệu này được cập nhật thường xuyên để phản ánh các thay đổi trong admin dashboard.*
