import { notFound } from "next/navigation"

import { getAdminProjectById } from "@/app/actions/admin"
import { getAllCategories } from "@/app/actions/projects"
import { AdminProjectEditForm } from "@/components/admin/admin-project-edit-form"

interface AdminProjectEditPageProps {
  params: Promise<{
    projectId: string
  }>
}

export default async function AdminProjectEditPage({ params }: AdminProjectEditPageProps) {
  const { projectId } = await params
  
  const [projectResult, categoriesData] = await Promise.all([
    getAdminProjectById(projectId),
    getAllCategories(),
  ])
  
  if (!projectResult.success || !projectResult.project) {
    notFound()
  }

  // Ensure featuredOnHomepage is boolean
  const projectWithDefaults = {
    ...projectResult.project,
    featuredOnHomepage: projectResult.project.featuredOnHomepage ?? false
  }

  return (
    <AdminProjectEditForm
      project={projectWithDefaults}
      categories={categoriesData}
    />
  )
}
